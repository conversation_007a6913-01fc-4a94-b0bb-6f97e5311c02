# -*- coding: utf-8 -*-

from typing import Optional

from dateutil import parser

from ..client.qb.handler import Handler
from ..client.qb.models import Platform
from ..client.tikhub_xhs_client import TikhubXhsClient
from .models.author_work import AuthorWork
from ..utils.string_utils import StringUtils
from ..utils.time_utils import TimeUtils

import re


class XhsDetailHandler:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.tikhub_xhs_client = TikhubXhsClient(logger_=logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    def query_article_detail(self, note_id):
        try:
            work_url = "https://www.xiaohongshu.com/discovery/item/" + note_id

            # 第一次请求清博接口
            item, should_retry = self._query_qingbo_with_retry(work_url)

            # 根据清博接口返回结果决定后续处理
            if item:
                # 清博接口成功，直接返回结果
                item_dict = item.to_dict()
                item_dict["id"] = note_id
                return self._generate_qingbo_article(item_dict)
            elif not should_retry:
                self.logger_.info(f"作品不存在  note_id: {note_id}")
                return None
            else:
                self.logger_.info(f"清博接口失败，尝试第三方接口，note_id: {note_id}")

                note_result = self.tikhub_xhs_client.fetch_feed_notes(note_id)
                if self.is_success(note_result):
                    return self._generate_tiktok_article(note_result.get("data"))

                note_result = self.tikhub_xhs_client.fetch_feed_notes_v2_2(note_id)
                if self.is_success(note_result):
                    return self.generate_tiktok_article_v2_2(note_result.get("data"))

                return None
        except Exception as e:
            self.logger_.error(f"query_article_detail error: {e}, note_id: {note_id}")
            return None

    def _query_qingbo_with_retry(self, work_url):
        """
        查询清博接口，处理重试逻辑
        返回: (item_data, should_retry_third_party)
        - item_data: ArticleDetail对象或None
        - should_retry_third_party: 是否应该重试第三方接口
        """
        try:
            # 第一次请求
            item = self.qingbo_handler.query_article_detail(key=work_url)
            if item:
                return item, False

            # 第一次请求失败，需要检查具体的错误码
            # 通过直接调用 api_client 来获取详细的响应信息
            try:
                api_response = self.qingbo_handler.client._make_request("/jss/api/get-detail", {
                    "platform": "xhs",
                    "news_url": work_url
                })

                # 解析响应
                from ..client.qb.models import ApiResponse
                parsed_response = ApiResponse.from_dict(api_response)

                if parsed_response.code == 404:
                    return None, False
                elif parsed_response.code == 500:
                    self.logger_.info(f"清博接口返回500，重试一次，work_url: {work_url}")
                    import time
                    time.sleep(1)

                    item = self.qingbo_handler.query_article_detail(key=work_url)
                    if item:
                        return item, False
                    else:
                        return None, True
                else:
                    # 其他错误，可以尝试第三方接口
                    return None, True

            except Exception as e:
                self.logger_.error(f"获取清博接口详细响应失败: {e}")
                return None, True

        except Exception as e:
            self.logger_.error(f"_query_qingbo_with_retry error: {e}")
            return None, True

    def query_article_detail_by_qb(self, note_id):
        try:
            work_url = "https://www.xiaohongshu.com/discovery/item/" + note_id
            item = self.qingbo_handler.query_article_detail(key=work_url)
            if item:
                item_dict = item.to_dict()
                item_dict["id"] = note_id

                return self._generate_qingbo_article(item_dict)
        except Exception as e:
            self.logger_.error(e)
            return None

    def is_success(self, data):
        if data is None:
            return False

        if "data" not in data:
            return False

        return True

    def _generate_tiktok_article(self, data):
        try:
            code = data.get("code")
            if code != 0:
                return None

            d = data.get("data")
            if len(d) == 0:
                return None

            notes = d[0]
            user = notes.get("user")
            author_name = user.get('name', '')
            author_id = user.get('userid', '')
            author_avatar = user.get('image', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            note_list = notes.get("note_list")
            if len(note_list) == 0:
                return None

            note = note_list[0]
            work_id = note.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title', '')
            content = note.get('desc', '')
            collect_count = note.get('collected_count', '')
            comment_count = note.get('comments_count', '')
            share_count = note.get('shared_count', '')
            like_count = note.get('liked_count', '')
            publish_time = TimeUtils.ts_to_str(note.get('time'))

            ip_location = note.get('ip_location', '')
            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")

            thumbnail_link = ''
            try:
                topics = note.get('topics')
                topic = topics[0]
                thumbnail_link = topic.get("image", "")
            except Exception as e:
                pass

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='',
                              music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip=ip_location, read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def generate_tiktok_article_v2_2(self, data):
        try:
            d = data.get("note_list")
            if len(d) == 0:
                return None

            user = data.get("user")
            author_name = user.get('name', '')
            author_id = user.get('userid', '')
            author_avatar = user.get('image', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            note_list = data.get("note_list")
            if len(note_list) == 0:
                return None

            note = note_list[0]
            work_id = note.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title', '')
            content = note.get('desc', '')
            collect_count = note.get('collected_count', '')
            comment_count = note.get('comments_count', '')
            share_count = note.get('share_count', '')
            like_count = note.get('likes', '')
            publish_time = TimeUtils.ts_to_str(note.get('create_time'))

            ip_location = note.get('ip_location', '')
            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")

            thumbnail_link = ''
            try:
                topics = note.get('images_list')
                topic = topics[0]
                thumbnail_link = topic.get("url", "")
            except Exception as e:
                pass

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='',
                              music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip=ip_location, read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def split_img_url_to_list(self, input_str: str) -> str:

        """
        切分清博的图片 url / video 地址，并返回以逗号分隔的字符串
        :param input_str: 输入的字符串，包含多个以分号分隔的 URL
        :return: 以逗号分隔的 URL 字符串（如 "url1,url2"）
        """
        if not input_str or not input_str.strip():
            return ""

        # 分割并过滤空值，然后用逗号连接
        url_list = [url.strip() for url in re.split(r'[;]', input_str) if url.strip()]
        return ",".join(url_list)

    def _generate_qingbo_article(self, item: dict) -> Optional[AuthorWork]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = item.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            image_urls_ = item.get("news_img_urls", "")
            image_urls = image_urls_
            try:
                image_urls = self.split_img_url_to_list(image_urls_)
            except Exception as e:
                print(e)

            news_video_urls = item.get("news_video_urls", "")
            video_urls = news_video_urls
            try:
                video_urls = self.split_img_url_to_list(news_video_urls)
            except Exception as e:
                print(e)

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls=image_urls, video_urls=video_urls,
                              music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"_generate_qingbo_article {StringUtils.obj_2_json_string(item)}, {e}")
            return None