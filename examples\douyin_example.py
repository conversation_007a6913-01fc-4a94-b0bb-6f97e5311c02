# -*- coding: utf-8 -*-
"""
抖音 API 使用示例

本示例演示如何使用 jss_api_extend 包获取抖音作品详情和搜索功能。
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend import DouyinDetailHandler, DouyinSearchHandler
from jss_api_extend.common import Env


async def douyin_detail_example():
    """抖音作品详情获取示例"""
    print("=== 抖音作品详情获取示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建抖音详情处理器
    douyin_handler = DouyinDetailHandler(logger)
    
    # 示例作品ID
    work_id = "7521675067002162467"
    
    try:
        # 获取作品详情
        print(f"正在获取作品ID {work_id} 的详情...")
        work_detail = await douyin_handler.query_article_detail(work_id)
        
        if work_detail:
            print("✅ 成功获取作品详情:")
            print(f"  标题: {work_detail.title}")
            print(f"  作者: {work_detail.author_name}")
            print(f"  点赞数: {work_detail.like_count}")
            print(f"  评论数: {work_detail.comment_count}")
            print(f"  分享数: {work_detail.share_count}")
            print(f"  发布时间: {work_detail.publish_time}")
            print(f"  作品链接: {work_detail.work_url}")
        else:
            print("❌ 未能获取作品详情")
            
    except Exception as e:
        print(f"❌ 获取作品详情时发生错误: {e}")


def douyin_search_example():
    """抖音搜索功能示例"""
    print("\n=== 抖音搜索功能示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建抖音搜索处理器
    search_handler = DouyinSearchHandler(logger)
    
    # 示例搜索关键词
    keyword = "美食"
    
    try:
        print(f"正在搜索关键词: {keyword}")
        
        # 这里可以添加搜索功能的调用
        # search_results = search_handler.search_videos(keyword, count=10)
        
        print("✅ 搜索功能演示完成")
        print("注意: 具体的搜索实现需要根据实际API接口调整")
        
    except Exception as e:
        print(f"❌ 搜索时发生错误: {e}")


async def main():
    """主函数"""
    print("🚀 JSS API Extension - 抖音功能演示")
    print("=" * 50)
    
    # 运行抖音详情获取示例
    await douyin_detail_example()
    
    # 运行抖音搜索示例
    douyin_search_example()
    
    print("\n✨ 演示完成!")


if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv('TIKHUB_API_KEY'):
        print("⚠️  警告: 未设置 TIKHUB_API_KEY 环境变量")
        print("请设置环境变量后再运行示例:")
        print("export TIKHUB_API_KEY='your_api_key'")
        print("export TIKHUB_BASE_URL='https://api.tikhub.io'")
        sys.exit(1)
    
    # 运行异步主函数
    asyncio.run(main())
