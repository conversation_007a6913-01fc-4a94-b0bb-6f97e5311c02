# -*- coding: utf-8 -*-

import json
import time
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any

from alibabacloud_dingtalk.notable_1_0 import models as dingtalknotable__1__0_models
from alibabacloud_dingtalk.notable_1_0.client import Client as dingtalknotable_1_0_Client
from alibabacloud_dingtalk.notable_1_0.models import *
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

from dingtalk_alerter import DingTalkAlerter
from ..client.model.dingtalk_bitable_record_info import DingtalkBitableRecordInfo, DingTalkBitableDMRecord
from ..utils.http_utils import JSSHttpUtils
from ..utils.time_utils import TimeUtils


class BaseDingTalkBitableNotifier(ABC):
    """
    钉钉多维表通知器基础类
    
    提供钉钉多维表操作的通用功能，包括：
    - 访问令牌管理
    - 通用的查询、更新、插入方法
    - 消息发送功能
    
    子类需要实现抽象方法来定制化业务逻辑：
    - get_operator_id(): 获取操作员ID
    - build_query_filter(): 构建查询过滤条件
    - parse_record_to_model(): 解析记录为业务模型
    - should_process_record(): 判断记录是否需要处理
    - build_update_data(): 构建更新数据
    """

    def __init__(self, logger_, app_key, app_secret, agent_id):
        self.logger_ = logger_
        self.client = self._create_client()
        self.access_token = None
        self.expire_time = int(time.time())
        self.headers = {
            "accept": "application/json"
        }

        self.bitable_url = "https://api.dingtalk.com/v1.0/notable/bases/"

        self.app_key = app_key
        self.app_secret = app_secret
        self.agent_id = agent_id

        self.ding_talk_alert = DingTalkAlerter()

    # ==================== 抽象方法 - 子类必须实现 ====================
    
    @abstractmethod
    def get_operator_id(self) -> str:
        """获取操作员ID - 不同客户可能有不同的操作员"""
        pass

    @abstractmethod
    def build_query_filter(self, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建查询过滤条件 - 不同客户有不同的查询逻辑"""
        pass

    @abstractmethod
    def parse_record_to_model(self, record) -> Any:
        """将API返回的记录解析为业务模型 - 不同客户有不同的数据结构"""
        pass

    @abstractmethod
    def should_process_record(self, record, **kwargs) -> bool:
        """判断记录是否需要处理 - 不同客户有不同的业务规则"""
        pass

    @abstractmethod
    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建更新数据 - 不同客户有不同的更新逻辑"""
        pass

    # ==================== 通用功能实现 ====================

    def _create_client(self) -> dingtalkoauth2_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkoauth2_1_0Client(config)

    def get_access_token(self):
        if self.access_token is None or int(time.time()) >= self.expire_time:
            self.__refresh_access_token()

        return self.access_token

    def __refresh_access_token(self):
        flag, access_token, expire_in = self._query_new_access_token()
        if flag:
            self.access_token = access_token
            self.expire_time = int(time.time()) + expire_in - 60 * 5

    def _query_new_access_token(self):
        get_access_token_request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
            app_key=self.app_key,
            app_secret=self.app_secret
        )

        try:
            response: dingtalkoauth_2__1__0_models.GetAccessTokenResponse = self.client.get_access_token(
                get_access_token_request)
            if response.status_code != 200:
                return False, None, None

            body: dingtalkoauth_2__1__0_models.GetAccessTokenResponseBody = response.body
            return True, body.access_token, body.expire_in
        except Exception as err:
            self.logger_.error(f"dingtalk get_access_token error={err}")

    def get_user_id_by_union_id(self, union_id):
        access_token = self.get_access_token()
        url = 'https://oapi.dingtalk.com/topapi/user/getbyunionid?access_token=' + access_token
        body = {
            "unionid": union_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=body)
            self.logger_.info(f'get_user_id_by_union_id={response.text}')
            json_str = response.content.decode('utf-8')
            data = json.loads(json_str)
            return data.get('result').get('userid')
        except Exception as e:
            self.logger_.error(e)

    def send_message(self, union_id, message):
        user_id = self.get_user_id_by_union_id(union_id)

        url = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=" + self.get_access_token()
        body = {
            "agent_id": self.agent_id,
            "msg": {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            },
            "userid_list": user_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=json.dumps(body))
            self.logger_.info(f"send_message={response.text}")
        except Exception as e:
            self.logger_.error(f"{e}")

    def _create_bitable_client(self) -> dingtalknotable_1_0_Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalknotable_1_0_Client(config)

    # ==================== 通用查询方法 ====================

    def list_bitable_data_generic(self, dentry_uuid: str, id_or_name: str, **kwargs) -> Optional[List[Any]]:
        """
        通用的多维表数据查询方法
        
        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            **kwargs: 传递给子类方法的额外参数
            
        Returns:
            解析后的记录列表
        """
        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()

        # 使用子类实现的过滤条件
        filter_condition = self.build_query_filter(**kwargs)

        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = self.get_operator_id()
        request.max_results = 100
        request.filter = filter_condition

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records

            for record in records:
                # 使用子类的业务规则判断是否处理该记录
                if self.should_process_record(record, **kwargs):
                    # 使用子类的解析方法转换为业务模型
                    parsed_record = self.parse_record_to_model(record)
                    bitable_record_list.append(parsed_record)

        return bitable_record_list

    # ==================== 通用更新方法 ====================

    def update_bitable_data_generic(self,
                                    dentry_uuid: str,
                                    id_or_name: str,
                                    record_updates: List[Dict[str, Any]]) -> bool:
        """
        通用的多维表数据更新方法
        
        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            record_updates: 更新数据列表，格式: [{"record_id": "xxx", "fields": {...}}, ...]
            
        Returns:
            更新是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.UpdateRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            records = []
            for update_data in record_updates:
                record = dingtalknotable__1__0_models.UpdateRecordsRequestRecords()
                record.id = update_data["record_id"]
                record.fields = update_data["fields"]
                records.append(record)

            request = dingtalknotable__1__0_models.UpdateRecordsRequest()
            request.operator_id = self.get_operator_id()
            request.records = records

            response: dingtalknotable__1__0_models.UpdateRecordsResponse = dingtalk_notable.update_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                self.logger_.error(
                    f"批量更新多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"同步多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            updated_count = len(record_updates)
            self.logger_.info(f"成功批量更新 {updated_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")
            return True

        except Exception as e:
            self.logger_.error(f"批量更新多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量更新多维表={dentry_uuid} 异常: {str(e)}")
            return False

    # ==================== 通用插入方法 ====================

    def batch_insert_bitable_data_generic(self,
                                          dentry_uuid: str,
                                          id_or_name: str,
                                          records_data: List[Dict[str, Any]]) -> bool:
        """
        通用的批量插入数据到钉钉多维表方法 (同步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            records_data (list): 要插入的记录数据列表，每个元素是包含字段数据的字典
                                格式: [{"field1": "value1", "field2": "value2"}, ...]

        Returns:
            bool: 插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = self.get_operator_id()
            request.records = records

            # 发送批量插入请求 (同步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = dingtalk_notable.insert_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(
                f"成功批量插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 异常: {str(e)}")
            return False

    async def batch_insert_bitable_data_generic_async(self,
                                                      dentry_uuid: str,
                                                      id_or_name: str,
                                                      records_data: List[Dict[str, Any]]) -> bool:
        """
        通用的批量插入数据到钉钉多维表方法 (异步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            records_data (list): 要插入的记录数据列表，每个元素是包含字段数据的字典
                                格式: [{"field1": "value1", "field2": "value2"}, ...]

        Returns:
            bool: 插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = self.get_operator_id()
            request.records = records

            # 发送批量插入请求 (异步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = await dingtalk_notable.insert_records_with_options_async(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(
                f"成功批量插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 异常: {str(e)}")
            return False

    # ==================== 辅助方法 ====================

    def _days_since_timestamp(self, millis_timestamp):
        """计算时间戳距离今天的天数"""
        import datetime

        # 将毫秒时间戳转换为秒
        timestamp_seconds = millis_timestamp / 1000
        # 转换为datetime对象
        timestamp_date = datetime.datetime.fromtimestamp(timestamp_seconds)
        # 获取当前日期（去掉时间部分）
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        # 计算日期差
        delta = today - timestamp_date.replace(hour=0, minute=0, second=0, microsecond=0)

        return delta.days

    def _is_match_7_days_rule(self, created_time, last_modified_time):
        """判断是否符合7天规则（7, 14, 21, 28, 35, 42, 49, 56天）"""
        diff_update_days = self._days_since_timestamp(last_modified_time)
        if diff_update_days == 0:
            return False

        diff_create_days = self._days_since_timestamp(created_time)
        return diff_create_days in {7 * n for n in range(1, 9)}

    def _is_match_3_days_rule(self, created_time, last_modified_time):
        """判断是否符合3天规则（3天和7天）"""
        diff_update_days = self._days_since_timestamp(last_modified_time)
        if diff_update_days == 0:
            return False

        diff_create_days = self._days_since_timestamp(created_time)
        return diff_create_days in {3, 7}
