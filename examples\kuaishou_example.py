# -*- coding: utf-8 -*-
"""
快手 API 使用示例

本示例演示如何使用 jss_api_extend 包获取快手作品详情。
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend import KuaiShouDetailHandler
from jss_api_extend.common import Env


def kuaishou_detail_example():
    """快手作品详情获取示例"""
    print("=== 快手作品详情获取示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建快手详情处理器
    ks_handler = KuaiShouDetailHandler(logger)
    
    # 示例作品URL
    work_urls = [
        "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6?authorId=3xntsdjqqfa9n29&streamSource=brilliant&hotChannelId=00&area=brilliantxxcarefully",
        "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6",
        # 可以添加更多测试URL
    ]
    
    for i, work_url in enumerate(work_urls, 1):
        print(f"\n--- 示例 {i} ---")
        print(f"作品URL: {work_url}")
        
        try:
            print("正在获取作品详情...")
            
            # 获取作品详情
            work_detail = ks_handler.query_article_detail_app_v1(work_url)
            
            if work_detail:
                print("✅ 成功获取作品详情:")
                print(f"  标题: {work_detail.title}")
                print(f"  作者: {work_detail.author_name}")
                print(f"  作者ID: {work_detail.author_id}")
                print(f"  点赞数: {work_detail.like_count}")
                print(f"  评论数: {work_detail.comment_count}")
                print(f"  分享数: {work_detail.share_count}")
                print(f"  播放数: {work_detail.play_count}")
                print(f"  发布时间: {work_detail.publish_time}")
                print(f"  作品链接: {work_detail.work_url}")
                
                # 显示视频信息
                if hasattr(work_detail, 'video_url') and work_detail.video_url:
                    print(f"  视频链接: {work_detail.video_url}")
                
                # 显示封面信息
                if hasattr(work_detail, 'cover_url') and work_detail.cover_url:
                    print(f"  封面链接: {work_detail.cover_url}")
                    
                # 显示标签信息
                if hasattr(work_detail, 'tags') and work_detail.tags:
                    print(f"  标签: {', '.join(work_detail.tags)}")
                    
                # 显示地理位置信息
                if hasattr(work_detail, 'location') and work_detail.location:
                    print(f"  地理位置: {work_detail.location}")
                    
            else:
                print("❌ 未能获取作品详情")
                
        except Exception as e:
            print(f"❌ 获取作品详情时发生错误: {e}")


def kuaishou_url_parsing_example():
    """快手URL解析示例"""
    print("\n=== 快手URL解析示例 ===")
    
    from jss_api_extend.utils import UrlUtils
    
    # 示例URL
    test_urls = [
        "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6?authorId=3xntsdjqqfa9n29",
        "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6",
        "https://v.kuaishou.com/AbCdEf",  # 短链接示例
    ]
    
    print("正在解析快手作品ID...")
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n  URL {i}: {url}")
        try:
            # 解析作品ID
            work_id = UrlUtils.get_kuaishou_work_id(url)
            if work_id:
                print(f"    ✅ 作品ID: {work_id}")
            else:
                print(f"    ❌ 无法解析作品ID")
        except Exception as e:
            print(f"    ❌ 解析错误: {e}")


def main():
    """主函数"""
    print("🚀 JSS API Extension - 快手功能演示")
    print("=" * 50)
    
    # 运行快手详情获取示例
    kuaishou_detail_example()
    
    # 运行URL解析示例
    kuaishou_url_parsing_example()
    
    print("\n✨ 演示完成!")
    print("\n💡 提示:")
    print("  - 确保提供的快手作品URL是有效的")
    print("  - 某些作品可能因为隐私设置无法获取详情")
    print("  - API调用频率请遵守平台限制")


if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv('TIKHUB_API_KEY'):
        print("⚠️  警告: 未设置 TIKHUB_API_KEY 环境变量")
        print("请设置环境变量后再运行示例:")
        print("export TIKHUB_API_KEY='your_api_key'")
        print("export TIKHUB_BASE_URL='https://api.tikhub.io'")
        sys.exit(1)
    
    # 运行主函数
    main()
