# -*- coding: utf-8 -*-
"""
JSS API Extension 综合使用示例

本示例演示如何综合使用 jss_api_extend 包的各种功能。
"""

import asyncio
import os
import sys
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend import (
    DouyinDetailHandler,
    XhsDetailHandler, 
    KuaiShouDetailHandler,
    DingTalkBitableNotifier
)
from jss_api_extend.common import Env
from jss_api_extend.utils import StringUtils, TimeUtils


class SocialMediaAnalyzer:
    """社交媒体数据分析器"""
    
    def __init__(self):
        self.logger = Env().get_main_logger()
        self.douyin_handler = DouyinDetailHandler(self.logger)
        self.xhs_handler = XhsDetailHandler(self.logger)
        self.ks_handler = KuaiShouDetailHandler(self.logger)
        
        # 钉钉多维表配置（如果有的话）
        self.bitable_notifier = None
        if self._check_dingtalk_config():
            self.bitable_notifier = DingTalkBitableNotifier(
                logger_=self.logger,
                app_key=os.getenv('DINGTALK_APP_KEY'),
                app_secret=os.getenv('DINGTALK_APP_SECRET'),
                agent_id=os.getenv('DINGTALK_AGENT_ID')
            )
    
    def _check_dingtalk_config(self):
        """检查钉钉配置是否完整"""
        required_vars = ['DINGTALK_APP_KEY', 'DINGTALK_APP_SECRET', 'DINGTALK_AGENT_ID']
        return all(os.getenv(var) for var in required_vars)
    
    async def analyze_douyin_content(self, work_id):
        """分析抖音内容"""
        print(f"📱 分析抖音作品: {work_id}")
        
        try:
            work_detail = await self.douyin_handler.query_article_detail(work_id)
            if work_detail:
                analysis = {
                    'platform': '抖音',
                    'work_id': work_id,
                    'title': work_detail.title,
                    'author': work_detail.author_name,
                    'like_count': work_detail.like_count,
                    'comment_count': work_detail.comment_count,
                    'share_count': work_detail.share_count,
                    'publish_time': work_detail.publish_time,
                    'engagement_rate': self._calculate_engagement_rate(
                        work_detail.like_count,
                        work_detail.comment_count,
                        work_detail.share_count
                    )
                }
                return analysis
        except Exception as e:
            self.logger.error(f"分析抖音内容失败: {e}")
        
        return None
    
    def analyze_xhs_content(self, note_id):
        """分析小红书内容"""
        print(f"📝 分析小红书笔记: {note_id}")
        
        try:
            note_detail = self.xhs_handler.query_article_detail(note_id)
            if note_detail:
                analysis = {
                    'platform': '小红书',
                    'note_id': note_id,
                    'title': note_detail.title,
                    'author': note_detail.author_name,
                    'like_count': note_detail.like_count,
                    'comment_count': note_detail.comment_count,
                    'collect_count': getattr(note_detail, 'collect_count', 0),
                    'share_count': note_detail.share_count,
                    'publish_time': note_detail.publish_time,
                    'engagement_rate': self._calculate_engagement_rate(
                        note_detail.like_count,
                        note_detail.comment_count,
                        note_detail.share_count
                    )
                }
                return analysis
        except Exception as e:
            self.logger.error(f"分析小红书内容失败: {e}")
        
        return None
    
    def analyze_kuaishou_content(self, work_url):
        """分析快手内容"""
        print(f"🎬 分析快手作品: {work_url}")
        
        try:
            work_detail = self.ks_handler.query_article_detail_app_v1(work_url)
            if work_detail:
                analysis = {
                    'platform': '快手',
                    'work_url': work_url,
                    'title': work_detail.title,
                    'author': work_detail.author_name,
                    'like_count': work_detail.like_count,
                    'comment_count': work_detail.comment_count,
                    'share_count': work_detail.share_count,
                    'play_count': getattr(work_detail, 'play_count', 0),
                    'publish_time': work_detail.publish_time,
                    'engagement_rate': self._calculate_engagement_rate(
                        work_detail.like_count,
                        work_detail.comment_count,
                        work_detail.share_count
                    )
                }
                return analysis
        except Exception as e:
            self.logger.error(f"分析快手内容失败: {e}")
        
        return None
    
    def _calculate_engagement_rate(self, likes, comments, shares):
        """计算互动率"""
        total_engagement = (likes or 0) + (comments or 0) + (shares or 0)
        # 这里简化处理，实际应该除以粉丝数或播放数
        return total_engagement
    
    def generate_report(self, analyses):
        """生成分析报告"""
        print("\n📊 生成综合分析报告")
        print("=" * 60)
        
        if not analyses:
            print("❌ 没有可分析的数据")
            return
        
        # 按平台分组
        platform_stats = {}
        total_engagement = 0
        
        for analysis in analyses:
            if not analysis:
                continue
                
            platform = analysis['platform']
            if platform not in platform_stats:
                platform_stats[platform] = {
                    'count': 0,
                    'total_likes': 0,
                    'total_comments': 0,
                    'total_shares': 0,
                    'total_engagement': 0
                }
            
            stats = platform_stats[platform]
            stats['count'] += 1
            stats['total_likes'] += analysis.get('like_count', 0) or 0
            stats['total_comments'] += analysis.get('comment_count', 0) or 0
            stats['total_shares'] += analysis.get('share_count', 0) or 0
            stats['total_engagement'] += analysis.get('engagement_rate', 0) or 0
            
            total_engagement += analysis.get('engagement_rate', 0) or 0
        
        # 输出报告
        print(f"📈 总体统计:")
        print(f"  分析内容数量: {len([a for a in analyses if a])}")
        print(f"  涉及平台: {', '.join(platform_stats.keys())}")
        print(f"  总互动量: {total_engagement}")
        
        print(f"\n📱 各平台详细统计:")
        for platform, stats in platform_stats.items():
            print(f"  {platform}:")
            print(f"    内容数量: {stats['count']}")
            print(f"    总点赞数: {stats['total_likes']}")
            print(f"    总评论数: {stats['total_comments']}")
            print(f"    总分享数: {stats['total_shares']}")
            print(f"    平均互动率: {stats['total_engagement'] / stats['count']:.2f}")
        
        # 保存到钉钉多维表（如果配置了）
        if self.bitable_notifier:
            self._save_to_bitable(analyses)
    
    def _save_to_bitable(self, analyses):
        """保存分析结果到钉钉多维表"""
        print(f"\n💾 保存分析结果到钉钉多维表...")
        
        try:
            # 这里需要根据实际的多维表结构调整
            table_data = []
            for analysis in analyses:
                if analysis:
                    table_data.append({
                        '平台': analysis['platform'],
                        '标题': analysis.get('title', ''),
                        '作者': analysis.get('author', ''),
                        '点赞数': analysis.get('like_count', 0),
                        '评论数': analysis.get('comment_count', 0),
                        '分享数': analysis.get('share_count', 0),
                        '互动率': analysis.get('engagement_rate', 0),
                        '发布时间': analysis.get('publish_time', ''),
                        '分析时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
            
            if table_data:
                # 这里需要替换为实际的表格UUID和工作表名称
                # success = self.bitable_notifier.batch_create_bitable_data(
                #     "your_table_uuid",
                #     "your_sheet_name", 
                #     table_data
                # )
                print(f"✅ 准备保存 {len(table_data)} 条记录到多维表")
                print("💡 请配置实际的表格UUID和工作表名称")
            
        except Exception as e:
            self.logger.error(f"保存到多维表失败: {e}")


async def main():
    """主函数"""
    print("🚀 JSS API Extension - 综合功能演示")
    print("=" * 60)
    
    # 创建分析器
    analyzer = SocialMediaAnalyzer()
    
    # 示例数据
    test_data = [
        ('douyin', '7521675067002162467'),
        ('xhs', '6863e1450000000024008d2b'),
        ('kuaishou', 'https://www.kuaishou.com/short-video/3xk4g2yryfjpin6'),
    ]
    
    analyses = []
    
    # 分析各平台内容
    for platform, content_id in test_data:
        try:
            if platform == 'douyin':
                analysis = await analyzer.analyze_douyin_content(content_id)
            elif platform == 'xhs':
                analysis = analyzer.analyze_xhs_content(content_id)
            elif platform == 'kuaishou':
                analysis = analyzer.analyze_kuaishou_content(content_id)
            else:
                continue
            
            analyses.append(analysis)
            
        except Exception as e:
            print(f"❌ 分析 {platform} 内容失败: {e}")
    
    # 生成综合报告
    analyzer.generate_report(analyses)
    
    print("\n✨ 综合演示完成!")


if __name__ == "__main__":
    # 检查基本环境变量
    if not os.getenv('TIKHUB_API_KEY'):
        print("⚠️  警告: 未设置 TIKHUB_API_KEY 环境变量")
        print("请设置环境变量后再运行示例:")
        print("export TIKHUB_API_KEY='your_api_key'")
        print("export TIKHUB_BASE_URL='https://api.tikhub.io'")
        sys.exit(1)
    
    # 运行异步主函数
    asyncio.run(main())
