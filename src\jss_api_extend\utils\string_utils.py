# -*- encoding:utf-8 -*-

import json
import datetime


from .json_encoder import JsonEncoder


class StringUtils:
    @staticmethod
    def obj_2_json_string(obj):
        return json.dumps(obj, ensure_ascii=False, cls=JsonEncoder, default=JsonEncoder.json_default)

    @staticmethod
    def datetime_2_string(dt):
        return datetime.datetime.strftime(dt, "%Y-%m-%d %H:%M:%S")

    @staticmethod
    def date_2_string(dt):
        if dt is None:
            return ""
        return datetime.datetime.strftime(dt, "%Y-%m-%d")

    @staticmethod
    def time_2_string(dt):
        if dt is None:
            return ""
        return datetime.datetime.strftime(dt, "%H:%M:%S")

    @staticmethod
    def o_2_string(d):
        if isinstance(d, float):
            return ""

        return d

