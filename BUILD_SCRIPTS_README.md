# Windows 构建脚本说明

本项目为 Windows 用户提供了两个构建脚本，作为 Makefile 的替代方案。

## 📁 脚本文件

- `build.bat` - Windows 批处理脚本（CMD）
- `build.ps1` - PowerShell 脚本

## 🚀 快速开始

### 使用批处理脚本 (build.bat)

```cmd
# 查看帮助
.\build.bat help

# 开发周期（格式化 + 检查 + 测试）
.\build.bat dev

# 构建包
.\build.bat build

# 发布到测试 PyPI
.\build.bat upload-test
```

### 使用 PowerShell 脚本 (build.ps1)

```powershell
# 查看帮助
.\build.ps1 help

# 开发周期（格式化 + 检查 + 测试）
.\build.ps1 dev

# 构建包
.\build.ps1 build

# 发布到测试 PyPI
.\build.ps1 upload-test
```

## 📋 可用命令

| 命令 | 描述 |
|------|------|
| `help` | 显示帮助信息 |
| `clean` | 清理构建文件和缓存 |
| `format` | 使用 black 和 isort 格式化代码 |
| `lint` | 运行 flake8 和 mypy 检查 |
| `test` | 运行 pytest 测试 |
| `build` | 构建 Python 包 |
| `upload-test` | 上传到 Test PyPI |
| `upload` | 上传到正式 PyPI |
| `check` | 运行所有检查（格式检查 + lint + 测试）|
| `dev` | 开发周期（格式化 + lint + 测试）|

## ⚙️ PowerShell 执行策略

首次运行 PowerShell 脚本时，可能需要设置执行策略：

```powershell
# 为当前用户设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者仅为当前会话设置
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process
```

## 🔧 依赖要求

确保已安装以下 Python 包：

```cmd
pip install build twine black isort flake8 mypy pytest
```

或者使用项目的开发依赖：

```cmd
pip install -e ".[dev,test]"
```

## 💡 使用建议

1. **日常开发**: 使用 `dev` 命令进行快速的开发周期
2. **发布前**: 使用 `check` 命令确保所有检查通过
3. **测试发布**: 先使用 `upload-test` 发布到测试环境
4. **正式发布**: 确认无误后使用 `upload` 发布到正式 PyPI

## 🐛 故障排除

### 权限问题
如果遇到权限错误，尝试：
- 以管理员身份运行终端
- 或使用用户级安装：`pip install --user package_name`

### 路径问题
- 确保在项目根目录运行脚本
- 检查 Python 是否在 PATH 中

### PowerShell 脚本无法运行
- 检查执行策略设置
- 确保使用 `.\build.ps1` 而不是 `build.ps1`

## 📚 更多信息

详细的发布流程请参考 [RELEASE.md](RELEASE.md) 文档。
