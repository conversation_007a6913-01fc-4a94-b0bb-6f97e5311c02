# -*- coding: utf-8 -*-

"""
钉钉多维表通知器的具体客户实现示例

本文件包含了几个具体的客户实现，展示如何继承 BaseDingTalkBitableNotifier 
来为不同客户定制化查询条件、数据解析和业务规则。
"""

from typing import Optional, Dict, Any
from alibabacloud_dingtalk.notable_1_0.models import ListRecordsRequestFilter, ListRecordsRequestFilterConditions

from .base_dingtalk_bitable_notifier import BaseDingTalkBitableNotifier
from ..client.model.dingtalk_bitable_record_info import DingtalkBitableRecordInfo, DingTalkBitableDMRecord
from ..utils.time_utils import TimeUtils


class DefaultDingTalkBitableNotifier(BaseDingTalkBitableNotifier):
    """
    默认实现，保持原有逻辑
    适用于一般的多维表操作场景
    """
    
    def __init__(self, logger_, app_key, app_secret, agent_id, operator_id="JQm5uuCxkjI6pjP08ZJ7aQiEiE"):
        super().__init__(logger_, app_key, app_secret, agent_id)
        self._operator_id = operator_id

    def get_operator_id(self) -> str:
        return self._operator_id

    def build_query_filter(self, query_type: str = "default", max_update_times=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建默认的查询过滤条件"""
        conditions = []
        
        if max_update_times is not None:
            update_times_condition = ListRecordsRequestFilterConditions()
            update_times_condition.field = "更新次数"
            update_times_condition.operator = 'lessEqual'
            update_times_condition.value = [max_update_times]
            conditions.append(update_times_condition)

        name_condition = ListRecordsRequestFilterConditions()
        name_condition.field = "作品链接"
        name_condition.operator = 'notEmpty'
        name_condition.value = ['']
        conditions.append(name_condition)

        if not conditions:
            return None

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> DingtalkBitableRecordInfo:
        """解析为默认的记录模型"""
        return DingtalkBitableRecordInfo(
            work_url=record.fields.get("作品链接"),
            update_count=record.fields.get("更新次数"),
            user_union_id=record.fields.get("运营人员"),
            row_id=record.id
        )

    def should_process_record(self, record, max_update_times=None, **kwargs) -> bool:
        """默认的记录处理判断逻辑"""
        if max_update_times is None:
            return True
            
        update_times = int(record.fields.get("更新次数", 0))
        if update_times >= max_update_times:
            return self._is_match_7_days_rule(record.created_time, record.last_modified_time)
        return True

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建默认的更新数据"""
        return {
            "record_id": record_id,
            "fields": kwargs.get("fields", {})
        }


class DMDingTalkBitableNotifier(BaseDingTalkBitableNotifier):
    """
    大毛客户的具体实现
    专门用于大毛餐饮表格的数据处理
    """
    
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

    def get_operator_id(self) -> str:
        return "C9KfMJuvGAysoLhyLXfGuwiEiE"

    def build_query_filter(self, query_type: str = "default", days_back=15, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建大毛客户的查询过滤条件"""
        conditions = []
        
        # 大于 N天前
        past_date = TimeUtils.get_past_date(days_back)
        date_filter_gt_condition = ListRecordsRequestFilterConditions()
        date_filter_gt_condition.field = "创建时间"
        date_filter_gt_condition.operator = 'greater'
        date_filter_gt_condition.value = [past_date]
        conditions.append(date_filter_gt_condition)

        # 链接不为空
        name_condition = ListRecordsRequestFilterConditions()
        name_condition.field = "链接"
        name_condition.operator = 'notEmpty'
        name_condition.value = ['']
        conditions.append(name_condition)

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> DingTalkBitableDMRecord:
        """解析为大毛客户的记录模型"""
        return DingTalkBitableDMRecord(
            work_url=record.fields.get("链接"),
            create_time=record.fields.get("创建时间"),
            row_id=record.id
        )

    def should_process_record(self, record, **kwargs) -> bool:
        """大毛客户的记录处理判断逻辑"""
        # 大毛客户目前没有特殊的过滤逻辑，直接返回True
        return True

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建大毛客户的更新数据"""
        return {
            "record_id": record_id,
            "fields": kwargs.get("fields", {})
        }


class TaoTianDingTalkBitableNotifier(BaseDingTalkBitableNotifier):
    """
    淘天客户的具体实现
    使用3天规则进行数据处理
    """
    
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

    def get_operator_id(self) -> str:
        return "C9KfMJuvGAysoLhyLXfGuwiEiE"

    def build_query_filter(self, query_type: str = "default", max_update_times=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建淘天客户的查询过滤条件"""
        conditions = []
        
        if max_update_times is not None:
            update_times_condition = ListRecordsRequestFilterConditions()
            update_times_condition.field = "更新次数"
            update_times_condition.operator = 'lessEqual'
            update_times_condition.value = [max_update_times]
            conditions.append(update_times_condition)

        name_condition = ListRecordsRequestFilterConditions()
        name_condition.field = "作品链接"
        name_condition.operator = 'notEmpty'
        name_condition.value = ['']
        conditions.append(name_condition)

        if not conditions:
            return None

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> DingtalkBitableRecordInfo:
        """解析为淘天客户的记录模型"""
        return DingtalkBitableRecordInfo(
            work_url=record.fields.get("作品链接"),
            update_count=record.fields.get("更新次数"),
            user_union_id=record.fields.get("运营人员"),
            row_id=record.id
        )

    def should_process_record(self, record, max_update_times=None, **kwargs) -> bool:
        """淘天客户的记录处理判断逻辑 - 使用3天规则"""
        if max_update_times is None:
            return True
            
        update_times = int(record.fields.get("更新次数", 0))
        if update_times >= max_update_times:
            # 使用3天规则而不是7天规则
            return self._is_match_3_days_rule(record.created_time, record.last_modified_time)
        return True

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建淘天客户的更新数据"""
        return {
            "record_id": record_id,
            "fields": kwargs.get("fields", {})
        }


class CustomFieldDingTalkBitableNotifier(BaseDingTalkBitableNotifier):
    """
    自定义字段客户实现示例
    展示如何处理不同的字段名称和数据结构
    """
    
    def __init__(self, logger_, app_key, app_secret, agent_id, 
                 operator_id="CUSTOM_OPERATOR", 
                 url_field="内容链接", 
                 count_field="处理次数",
                 user_field="负责人"):
        super().__init__(logger_, app_key, app_secret, agent_id)
        self._operator_id = operator_id
        self.url_field = url_field
        self.count_field = count_field
        self.user_field = user_field

    def get_operator_id(self) -> str:
        return self._operator_id

    def build_query_filter(self, query_type: str = "default", max_count=None, status=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建自定义字段的查询过滤条件"""
        conditions = []
        
        if max_count is not None:
            count_condition = ListRecordsRequestFilterConditions()
            count_condition.field = self.count_field
            count_condition.operator = 'lessEqual'
            count_condition.value = [max_count]
            conditions.append(count_condition)

        if status:
            status_condition = ListRecordsRequestFilterConditions()
            status_condition.field = "状态"
            status_condition.operator = 'equal'
            status_condition.value = [status]
            conditions.append(status_condition)

        # URL不为空
        url_condition = ListRecordsRequestFilterConditions()
        url_condition.field = self.url_field
        url_condition.operator = 'notEmpty'
        url_condition.value = ['']
        conditions.append(url_condition)

        if not conditions:
            return None

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> Dict[str, Any]:
        """解析为自定义的记录模型"""
        return {
            'id': record.id,
            'url': record.fields.get(self.url_field),
            'count': record.fields.get(self.count_field, 0),
            'user': record.fields.get(self.user_field),
            'status': record.fields.get("状态"),
            'created_time': record.created_time,
            'last_modified_time': record.last_modified_time
        }

    def should_process_record(self, record, min_priority=1, **kwargs) -> bool:
        """自定义的记录处理判断逻辑"""
        priority = record.fields.get("优先级", 0)
        return priority >= min_priority

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建自定义的更新数据"""
        fields = kwargs.get("fields", {})
        
        # 添加更新时间戳
        import time
        fields["最后处理时间"] = int(time.time() * 1000)
        
        return {
            "record_id": record_id,
            "fields": fields
        }
