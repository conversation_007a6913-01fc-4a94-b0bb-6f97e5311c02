# -*- coding: utf-8 -*-
"""
钉钉多维表 API 使用示例

本示例演示如何使用 jss_api_extend 包进行钉钉多维表操作。
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend import DingTalkBitableNotifier, DingtalkBitableFactory
from jss_api_extend.common import Env


def bitable_query_example():
    """钉钉多维表查询示例"""
    print("=== 钉钉多维表查询示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 从环境变量获取配置
    app_key = os.getenv('DINGTALK_APP_KEY')
    app_secret = os.getenv('DINGTALK_APP_SECRET')
    agent_id = os.getenv('DINGTALK_AGENT_ID')
    
    if not all([app_key, app_secret, agent_id]):
        print("❌ 缺少钉钉配置信息，请设置环境变量")
        return
    
    try:
        # 创建钉钉多维表通知器
        notifier = DingTalkBitableNotifier(
            logger_=logger,
            app_key=app_key,
            app_secret=app_secret,
            agent_id=agent_id
        )
        
        # 示例多维表配置
        dentry_uuid = "your_table_uuid"  # 替换为实际的表格UUID
        sheet_name = "your_sheet_name"   # 替换为实际的工作表名称
        
        print(f"正在查询多维表数据...")
        print(f"  表格UUID: {dentry_uuid}")
        print(f"  工作表名称: {sheet_name}")
        
        # 查询多维表数据
        records = notifier.list_bitable_data_by_api_filter(
            dentry_uuid, 
            sheet_name, 
            max_update_times=5
        )
        
        if records:
            print(f"✅ 成功查询到 {len(records)} 条记录")
            
            # 显示前几条记录
            for i, record in enumerate(records[:3]):
                print(f"  记录 {i+1}: {record}")
        else:
            print("❌ 未查询到数据")
            
    except Exception as e:
        print(f"❌ 查询多维表数据时发生错误: {e}")


def bitable_create_example():
    """钉钉多维表创建数据示例"""
    print("\n=== 钉钉多维表创建数据示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 从环境变量获取配置
    app_key = os.getenv('DINGTALK_APP_KEY')
    app_secret = os.getenv('DINGTALK_APP_SECRET')
    agent_id = os.getenv('DINGTALK_AGENT_ID')
    
    if not all([app_key, app_secret, agent_id]):
        print("❌ 缺少钉钉配置信息，请设置环境变量")
        return
    
    try:
        # 创建钉钉多维表通知器
        notifier = DingTalkBitableNotifier(
            logger_=logger,
            app_key=app_key,
            app_secret=app_secret,
            agent_id=agent_id
        )
        
        # 示例数据
        dentry_uuid = "your_table_uuid"  # 替换为实际的表格UUID
        sheet_name = "your_sheet_name"   # 替换为实际的工作表名称
        
        # 要创建的数据
        data_to_create = [
            {
                "字段1": "值1",
                "字段2": "值2",
                "字段3": "值3"
            },
            {
                "字段1": "值4", 
                "字段2": "值5",
                "字段3": "值6"
            }
        ]
        
        print(f"正在创建多维表数据...")
        print(f"  要创建的记录数: {len(data_to_create)}")
        
        # 批量创建数据
        success = notifier.batch_create_bitable_data(
            dentry_uuid,
            sheet_name,
            data_to_create
        )
        
        if success:
            print("✅ 成功创建数据")
        else:
            print("❌ 创建数据失败")
            
    except Exception as e:
        print(f"❌ 创建多维表数据时发生错误: {e}")


def bitable_factory_example():
    """钉钉多维表工厂模式示例"""
    print("\n=== 钉钉多维表工厂模式示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    try:
        # 使用工厂模式创建不同公司的多维表客户端
        company_name = "default"  # 可以是 "taotian" 或其他公司名称
        
        print(f"正在创建 {company_name} 公司的多维表客户端...")
        
        # 通过工厂创建客户端
        bitable_client = DingtalkBitableFactory.create_bitable_notifier(
            company_name, logger
        )
        
        if bitable_client:
            print("✅ 成功创建多维表客户端")
            print(f"  客户端类型: {type(bitable_client).__name__}")
        else:
            print("❌ 创建多维表客户端失败")
            
    except Exception as e:
        print(f"❌ 创建多维表客户端时发生错误: {e}")


def main():
    """主函数"""
    print("🚀 JSS API Extension - 钉钉多维表功能演示")
    print("=" * 60)
    
    # 运行查询示例
    bitable_query_example()
    
    # 运行创建数据示例
    bitable_create_example()
    
    # 运行工厂模式示例
    bitable_factory_example()
    
    print("\n✨ 演示完成!")


if __name__ == "__main__":
    # 检查环境变量
    required_vars = ['DINGTALK_APP_KEY', 'DINGTALK_APP_SECRET', 'DINGTALK_AGENT_ID']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("⚠️  警告: 缺少以下环境变量:")
        for var in missing_vars:
            print(f"  - {var}")
        print("\n请设置环境变量后再运行示例:")
        print("export DINGTALK_APP_KEY='your_app_key'")
        print("export DINGTALK_APP_SECRET='your_app_secret'")
        print("export DINGTALK_AGENT_ID='your_agent_id'")
        sys.exit(1)
    
    # 运行主函数
    main()
