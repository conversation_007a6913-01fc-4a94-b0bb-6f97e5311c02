# -*- coding: utf-8 -*-

"""
增强版客户实现 - 支持多种查询条件

本文件展示了如何在一个客户实现中支持多种不同的查询条件，
而不需要为每种查询创建单独的继承类。
"""

from typing import Optional, Dict, Any, List
from alibabacloud_dingtalk.notable_1_0.models import ListRecordsRequestFilter, ListRecordsRequestFilterConditions

from .base_dingtalk_bitable_notifier import BaseDingTalkBitableNotifier
from ..client.model.dingtalk_bitable_record_info import DingtalkBitableRecordInfo, DingTalkBitableDMRecord
from ..utils.time_utils import TimeUtils


class EnhancedDMDingTalkBitableNotifier(BaseDingTalkBitableNotifier):
    """
    增强版大毛客户实现 - 支持多种查询条件
    
    支持的查询类型：
    - 'recent': 查询最近N天的记录
    - 'by_status': 按状态查询
    - 'by_user': 按用户查询
    - 'by_url': 按链接查询
    - 'pending_review': 查询待审核的记录
    - 'high_priority': 查询高优先级记录
    """
    
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

    def get_operator_id(self) -> str:
        return "C9KfMJuvGAysoLhyLXfGuwiEiE"

    def build_query_filter(self, query_type: str = "default", **kwargs) -> Optional[ListRecordsRequestFilter]:
        """
        根据查询类型构建不同的过滤条件
        
        Args:
            query_type: 查询类型
            **kwargs: 查询参数
        """
        if query_type == "recent":
            return self._build_recent_filter(**kwargs)
        elif query_type == "by_status":
            return self._build_status_filter(**kwargs)
        elif query_type == "by_user":
            return self._build_user_filter(**kwargs)
        elif query_type == "by_url":
            return self._build_url_filter(**kwargs)
        elif query_type == "pending_review":
            return self._build_pending_review_filter(**kwargs)
        elif query_type == "high_priority":
            return self._build_high_priority_filter(**kwargs)
        elif query_type == "complex":
            return self._build_complex_filter(**kwargs)
        else:
            return self._build_default_filter(**kwargs)

    def _build_recent_filter(self, days_back=15, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建最近N天的查询条件"""
        conditions = []
        
        # 大于 N天前
        past_date = TimeUtils.get_past_date(days_back)
        date_condition = ListRecordsRequestFilterConditions()
        date_condition.field = "创建时间"
        date_condition.operator = 'greater'
        date_condition.value = [past_date]
        conditions.append(date_condition)

        # 链接不为空
        url_condition = ListRecordsRequestFilterConditions()
        url_condition.field = "链接"
        url_condition.operator = 'notEmpty'
        url_condition.value = ['']
        conditions.append(url_condition)

        return self._create_filter(conditions, "and")

    def _build_status_filter(self, status=None, exclude_status=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建按状态查询的条件"""
        conditions = []
        
        if status:
            status_condition = ListRecordsRequestFilterConditions()
            status_condition.field = "状态"
            status_condition.operator = 'equal'
            status_condition.value = [status]
            conditions.append(status_condition)
        
        if exclude_status:
            exclude_condition = ListRecordsRequestFilterConditions()
            exclude_condition.field = "状态"
            exclude_condition.operator = 'notEqual'
            exclude_condition.value = [exclude_status]
            conditions.append(exclude_condition)

        return self._create_filter(conditions, "and") if conditions else None

    def _build_user_filter(self, user_id=None, user_list=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建按用户查询的条件"""
        conditions = []
        
        if user_id:
            user_condition = ListRecordsRequestFilterConditions()
            user_condition.field = "负责人"
            user_condition.operator = 'equal'
            user_condition.value = [user_id]
            conditions.append(user_condition)
        
        if user_list:
            user_list_condition = ListRecordsRequestFilterConditions()
            user_list_condition.field = "负责人"
            user_list_condition.operator = 'in'
            user_list_condition.value = user_list
            conditions.append(user_list_condition)

        return self._create_filter(conditions, "or") if conditions else None

    def _build_url_filter(self, url_contains=None, url_exact=None, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建按链接查询的条件"""
        conditions = []
        
        if url_exact:
            url_condition = ListRecordsRequestFilterConditions()
            url_condition.field = "链接"
            url_condition.operator = 'equal'
            url_condition.value = [url_exact]
            conditions.append(url_condition)
        
        if url_contains:
            contains_condition = ListRecordsRequestFilterConditions()
            contains_condition.field = "链接"
            contains_condition.operator = 'contains'
            contains_condition.value = [url_contains]
            conditions.append(contains_condition)

        return self._create_filter(conditions, "and") if conditions else None

    def _build_pending_review_filter(self, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建待审核记录的查询条件"""
        conditions = []
        
        # 状态为待审核
        status_condition = ListRecordsRequestFilterConditions()
        status_condition.field = "状态"
        status_condition.operator = 'equal'
        status_condition.value = ["待审核"]
        conditions.append(status_condition)
        
        # 创建时间在最近7天内
        past_date = TimeUtils.get_past_date(7)
        date_condition = ListRecordsRequestFilterConditions()
        date_condition.field = "创建时间"
        date_condition.operator = 'greater'
        date_condition.value = [past_date]
        conditions.append(date_condition)

        return self._create_filter(conditions, "and")

    def _build_high_priority_filter(self, min_priority=3, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建高优先级记录的查询条件"""
        conditions = []
        
        # 优先级大于等于指定值
        priority_condition = ListRecordsRequestFilterConditions()
        priority_condition.field = "优先级"
        priority_condition.operator = 'greaterEqual'
        priority_condition.value = [min_priority]
        conditions.append(priority_condition)
        
        # 状态不是已完成
        status_condition = ListRecordsRequestFilterConditions()
        status_condition.field = "状态"
        status_condition.operator = 'notEqual'
        status_condition.value = ["已完成"]
        conditions.append(status_condition)

        return self._create_filter(conditions, "and")

    def _build_complex_filter(self, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建复杂的组合查询条件"""
        conditions = []
        
        # 可以根据传入的参数动态构建条件
        if kwargs.get('status'):
            status_condition = ListRecordsRequestFilterConditions()
            status_condition.field = "状态"
            status_condition.operator = 'equal'
            status_condition.value = [kwargs['status']]
            conditions.append(status_condition)
        
        if kwargs.get('min_priority'):
            priority_condition = ListRecordsRequestFilterConditions()
            priority_condition.field = "优先级"
            priority_condition.operator = 'greaterEqual'
            priority_condition.value = [kwargs['min_priority']]
            conditions.append(priority_condition)
        
        if kwargs.get('days_back'):
            past_date = TimeUtils.get_past_date(kwargs['days_back'])
            date_condition = ListRecordsRequestFilterConditions()
            date_condition.field = "创建时间"
            date_condition.operator = 'greater'
            date_condition.value = [past_date]
            conditions.append(date_condition)
        
        if kwargs.get('user_list'):
            user_condition = ListRecordsRequestFilterConditions()
            user_condition.field = "负责人"
            user_condition.operator = 'in'
            user_condition.value = kwargs['user_list']
            conditions.append(user_condition)

        combination = kwargs.get('combination', 'and')
        return self._create_filter(conditions, combination) if conditions else None

    def _build_default_filter(self, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建默认查询条件"""
        return self._build_recent_filter(days_back=15, **kwargs)

    def _create_filter(self, conditions: List[ListRecordsRequestFilterConditions], 
                      combination: str = "and") -> ListRecordsRequestFilter:
        """创建过滤器对象"""
        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = combination
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> DingTalkBitableDMRecord:
        """解析为大毛客户的记录模型"""
        return DingTalkBitableDMRecord(
            work_url=record.fields.get("链接"),
            create_time=record.fields.get("创建时间"),
            row_id=record.id
        )

    def should_process_record(self, record, **kwargs) -> bool:
        """记录处理判断逻辑"""
        # 可以根据不同的查询类型应用不同的处理逻辑
        query_type = kwargs.get('query_type', 'default')
        
        if query_type == "high_priority":
            priority = record.fields.get("优先级", 0)
            return priority >= kwargs.get('min_priority', 3)
        elif query_type == "pending_review":
            status = record.fields.get("状态")
            return status == "待审核"
        else:
            return True

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建更新数据"""
        return {
            "record_id": record_id,
            "fields": kwargs.get("fields", {})
        }

    # ==================== 便捷方法 ====================
    
    def query_recent_records(self, dentry_uuid: str, id_or_name: str, days_back: int = 15):
        """查询最近N天的记录"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="recent",
            days_back=days_back
        )
    
    def query_by_status(self, dentry_uuid: str, id_or_name: str, status: str):
        """按状态查询记录"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="by_status",
            status=status
        )
    
    def query_by_user(self, dentry_uuid: str, id_or_name: str, user_id: str = None, user_list: List[str] = None):
        """按用户查询记录"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="by_user",
            user_id=user_id,
            user_list=user_list
        )
    
    def query_pending_review(self, dentry_uuid: str, id_or_name: str):
        """查询待审核记录"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="pending_review"
        )
    
    def query_high_priority(self, dentry_uuid: str, id_or_name: str, min_priority: int = 3):
        """查询高优先级记录"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="high_priority",
            min_priority=min_priority
        )
    
    def query_complex(self, dentry_uuid: str, id_or_name: str, **conditions):
        """复杂条件查询"""
        return self.list_bitable_data_generic(
            dentry_uuid=dentry_uuid,
            id_or_name=id_or_name,
            query_type="complex",
            **conditions
        )
