# -*- coding: utf-8 -*-

"""
钉钉多维表通知器使用示例

本文件展示了如何使用基础类和各种客户实现来处理不同的业务场景。
"""

import logging
from typing import List, Dict, Any

from .client_implementations import (
    DefaultDingTalkBitableNotifier,
    DMDingTalkBitableNotifier,
    TaoTianDingTalkBitableNotifier,
    CustomFieldDingTalkBitableNotifier
)


def setup_logger():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def example_default_implementation():
    """示例1: 使用默认实现"""
    logger = setup_logger()
    
    # 配置信息（实际使用时请替换为真实的配置）
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("=== 默认实现示例 ===")
    
    # 创建默认实现实例
    notifier = DefaultDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id,
        operator_id="custom_operator_id"  # 可选，使用自定义操作员ID
    )
    
    try:
        # 查询数据
        records = notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            max_update_times=5  # 传递给过滤条件的参数
        )
        
        print(f"查询到 {len(records) if records else 0} 条记录")
        
        if records and len(records) > 0:
            # 显示第一条记录
            first_record = records[0]
            print(f"第一条记录: URL={first_record.work_url}, 更新次数={first_record.update_count}")
            
            # 更新数据示例
            update_data = [
                notifier.build_update_data(
                    record_id=first_record.row_id,
                    fields={"更新次数": first_record.update_count + 1, "状态": "已处理"}
                )
            ]
            
            success = notifier.update_bitable_data_generic(
                dentry_uuid="your_base_id",
                id_or_name="your_sheet_name",
                record_updates=update_data
            )
            print(f"更新结果: {'成功' if success else '失败'}")
            
    except Exception as e:
        print(f"默认实现操作失败: {e}")


def example_dm_implementation():
    """示例2: 使用大毛客户实现"""
    logger = setup_logger()
    
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("\n=== 大毛客户实现示例 ===")
    
    # 创建大毛客户实现实例
    dm_notifier = DMDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        # 查询最近15天的数据
        records = dm_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            days_back=15  # 大毛客户特有的参数
        )
        
        print(f"查询到 {len(records) if records else 0} 条大毛客户记录")
        
        if records:
            for i, record in enumerate(records[:3]):  # 显示前3条
                print(f"记录 {i+1}: URL={record.work_url}, 创建时间={record.create_time}")
                
    except Exception as e:
        print(f"大毛客户实现操作失败: {e}")


def example_taotian_implementation():
    """示例3: 使用淘天客户实现（3天规则）"""
    logger = setup_logger()
    
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("\n=== 淘天客户实现示例 ===")
    
    # 创建淘天客户实现实例
    tt_notifier = TaoTianDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        # 查询数据，使用3天规则
        records = tt_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            max_update_times=3  # 淘天客户的更新次数限制
        )
        
        print(f"查询到 {len(records) if records else 0} 条淘天客户记录")
        
    except Exception as e:
        print(f"淘天客户实现操作失败: {e}")


def example_custom_field_implementation():
    """示例4: 使用自定义字段实现"""
    logger = setup_logger()
    
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("\n=== 自定义字段实现示例 ===")
    
    # 创建自定义字段实现实例
    custom_notifier = CustomFieldDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id,
        operator_id="CUSTOM_OPERATOR_123",
        url_field="内容链接",      # 自定义URL字段名
        count_field="处理次数",    # 自定义计数字段名
        user_field="负责人"       # 自定义用户字段名
    )
    
    try:
        # 查询数据，使用自定义参数
        records = custom_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            max_count=5,           # 自定义的最大处理次数
            status="待处理",        # 状态过滤
            min_priority=2         # 最小优先级
        )
        
        print(f"查询到 {len(records) if records else 0} 条自定义字段记录")
        
        if records:
            for record in records[:2]:  # 显示前2条
                print(f"记录: URL={record['url']}, 处理次数={record['count']}, 负责人={record['user']}")
                
    except Exception as e:
        print(f"自定义字段实现操作失败: {e}")


def example_batch_operations():
    """示例5: 批量操作示例"""
    logger = setup_logger()
    
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("\n=== 批量操作示例 ===")
    
    notifier = DefaultDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        # 批量插入数据
        insert_data = [
            {"作品链接": "https://example.com/1", "更新次数": 0, "运营人员": "张三"},
            {"作品链接": "https://example.com/2", "更新次数": 0, "运营人员": "李四"},
            {"作品链接": "https://example.com/3", "更新次数": 0, "运营人员": "王五"}
        ]
        
        insert_success = notifier.batch_insert_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            records_data=insert_data
        )
        print(f"批量插入结果: {'成功' if insert_success else '失败'}")
        
        # 批量更新数据
        update_data = [
            {"record_id": "record_id_1", "fields": {"更新次数": 1, "状态": "已处理"}},
            {"record_id": "record_id_2", "fields": {"更新次数": 1, "状态": "已处理"}},
        ]
        
        update_success = notifier.update_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            record_updates=update_data
        )
        print(f"批量更新结果: {'成功' if update_success else '失败'}")
        
    except Exception as e:
        print(f"批量操作失败: {e}")


def example_message_sending():
    """示例6: 消息发送示例"""
    logger = setup_logger()
    
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    print("\n=== 消息发送示例 ===")
    
    notifier = DefaultDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        # 发送消息给用户
        union_id = "user_union_id"
        message = "您好，您的任务已经处理完成！"
        
        notifier.send_message(union_id, message)
        print("消息发送完成")
        
    except Exception as e:
        print(f"消息发送失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("钉钉多维表通知器使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_default_implementation()
    example_dm_implementation()
    example_taotian_implementation()
    example_custom_field_implementation()
    example_batch_operations()
    example_message_sending()
    
    print("\n所有示例运行完成！")
    print("\n注意：")
    print("1. 请将示例中的配置信息替换为真实的钉钉应用配置")
    print("2. 请将 dentry_uuid 和 id_or_name 替换为真实的表格信息")
    print("3. 实际使用时请根据具体业务需求调整参数")


if __name__ == "__main__":
    main()
