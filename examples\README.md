# JSS API Extension 示例代码

本目录包含了 JSS API Extension 包的各种使用示例，帮助您快速上手和理解各个功能模块的用法。

## 📁 示例文件说明

### 1. `douyin_example.py` - 抖音功能示例
演示如何使用抖音相关功能：
- 获取抖音作品详情
- 抖音搜索功能演示
- 异步操作示例

**运行方式:**
```bash
cd examples
python douyin_example.py
```

### 2. `xiaohongshu_example.py` - 小红书功能示例
演示如何使用小红书相关功能：
- 获取小红书笔记详情
- 获取笔记评论
- 数据解析和展示

**运行方式:**
```bash
cd examples
python xiaohongshu_example.py
```

### 3. `kuaishou_example.py` - 快手功能示例
演示如何使用快手相关功能：
- 获取快手作品详情
- URL解析功能
- 多种URL格式支持

**运行方式:**
```bash
cd examples
python kuaishou_example.py
```

### 4. `dingtalk_bitable_example.py` - 钉钉多维表示例
演示如何使用钉钉多维表功能：
- 查询多维表数据
- 批量创建数据
- 工厂模式使用
- 多公司配置支持

**运行方式:**
```bash
cd examples
python dingtalk_bitable_example.py
```

### 5. `comprehensive_example.py` - 综合功能示例
演示如何综合使用多个功能模块：
- 多平台数据分析
- 数据统计和报告生成
- 结果保存到钉钉多维表
- 完整的工作流程

**运行方式:**
```bash
cd examples
python comprehensive_example.py
```

## 🔧 环境配置

在运行示例之前，请确保已正确配置环境变量：

### 基础配置（所有示例都需要）
```bash
export TIKHUB_API_KEY="your_tikhub_api_key"
export TIKHUB_BASE_URL="https://api.tikhub.io"
```

### 钉钉多维表配置（钉钉相关示例需要）
```bash
export DINGTALK_APP_KEY="your_dingtalk_app_key"
export DINGTALK_APP_SECRET="your_dingtalk_app_secret"
export DINGTALK_AGENT_ID="your_dingtalk_agent_id"
```

### 飞书配置（如果使用飞书功能）
```bash
export LARK_APP_ID="your_lark_app_id"
export LARK_APP_SECRET="your_lark_app_secret"
```

## 📋 运行前准备

1. **安装依赖包**
   ```bash
   pip install jss-api-extend
   ```

2. **设置环境变量**
   根据上面的配置说明设置相应的环境变量

3. **准备测试数据**
   - 抖音作品ID：可以从抖音分享链接中提取
   - 小红书笔记ID：可以从小红书分享链接中提取
   - 快手作品URL：完整的快手作品链接
   - 钉钉多维表UUID：从钉钉多维表获取

## 🚀 快速开始

1. **运行单个平台示例**
   ```bash
   # 测试抖音功能
   python examples/douyin_example.py
   
   # 测试小红书功能
   python examples/xiaohongshu_example.py
   
   # 测试快手功能
   python examples/kuaishou_example.py
   ```

2. **运行钉钉多维表示例**
   ```bash
   # 需要先配置钉钉相关环境变量
   python examples/dingtalk_bitable_example.py
   ```

3. **运行综合示例**
   ```bash
   # 演示完整的工作流程
   python examples/comprehensive_example.py
   ```

## 💡 使用提示

### 数据获取
- **作品ID获取**: 可以从各平台的分享链接中提取
- **API限制**: 注意各平台的API调用频率限制
- **数据时效性**: 社交媒体数据更新频繁，请注意数据的时效性

### 错误处理
- 所有示例都包含了基本的错误处理
- 建议在生产环境中添加更详细的日志记录
- 网络异常和API限制是常见的错误类型

### 性能优化
- 对于大量数据处理，建议使用异步方法
- 合理设置请求间隔，避免触发API限制
- 使用批量操作提高效率

## 🔍 故障排除

### 常见问题

1. **环境变量未设置**
   ```
   错误: 未设置 TIKHUB_API_KEY 环境变量
   解决: 检查并设置所需的环境变量
   ```

2. **API密钥无效**
   ```
   错误: API调用失败，返回401
   解决: 检查API密钥是否正确和有效
   ```

3. **网络连接问题**
   ```
   错误: 连接超时或网络错误
   解决: 检查网络连接，可能需要代理设置
   ```

4. **数据格式错误**
   ```
   错误: 无法解析返回的数据
   解决: 检查输入的ID或URL格式是否正确
   ```

### 调试建议

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查API响应**
   ```python
   # 在代码中添加响应检查
   print(f"API响应: {response}")
   ```

3. **逐步测试**
   - 先测试单个功能
   - 确认配置正确后再运行综合示例

## 📚 更多资源

- [项目主页](https://github.com/jinsuosuo/jss-api-extend)
- [API文档](https://github.com/jinsuosuo/jss-api-extend#api-文档)
- [问题反馈](https://github.com/jinsuosuo/jss-api-extend/issues)

---

如果您在使用过程中遇到问题，欢迎提交Issue或联系我们！
