[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
markers =
    integration: marks tests as integration tests (deselect with '-m "not integration"')
    slow: marks tests as slow (deselect with '-m "not slow"')
    unit: marks tests as unit tests
    performance: marks tests as performance tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
