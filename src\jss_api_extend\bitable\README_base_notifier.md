# 钉钉多维表通知器基础类使用指南

## 概述

`BaseDingTalkBitableNotifier` 是一个抽象基类，提供了钉钉多维表操作的通用功能。通过继承这个基类，不同的客户可以轻松实现自己的业务逻辑，而无需重复编写钉钉API调用、认证管理等通用代码。

## 架构设计

### 基础类 (`BaseDingTalkBitableNotifier`)

提供以下通用功能：
- 🔐 **访问令牌管理**: 自动获取和刷新钉钉访问令牌
- 📊 **通用查询方法**: `list_bitable_data_generic()` 
- ✏️ **通用更新方法**: `update_bitable_data_generic()`
- ➕ **通用插入方法**: `batch_insert_bitable_data_generic()` (同步/异步)
- 📨 **消息发送功能**: `send_message()`
- 🛠️ **辅助工具方法**: 时间计算、规则判断等

### 抽象方法 (子类必须实现)

```python
@abstractmethod
def get_operator_id(self) -> str:
    """获取操作员ID - 不同客户可能有不同的操作员"""
    pass

@abstractmethod  
def build_query_filter(self, **kwargs) -> Optional[ListRecordsRequestFilter]:
    """构建查询过滤条件 - 不同客户有不同的查询逻辑"""
    pass

@abstractmethod
def parse_record_to_model(self, record) -> Any:
    """将API返回的记录解析为业务模型 - 不同客户有不同的数据结构"""
    pass

@abstractmethod
def should_process_record(self, record, **kwargs) -> bool:
    """判断记录是否需要处理 - 不同客户有不同的业务规则"""
    pass

@abstractmethod
def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
    """构建更新数据 - 不同客户有不同的更新逻辑"""
    pass
```

## 快速开始

### 1. 创建自定义实现

```python
from .base_dingtalk_bitable_notifier import BaseDingTalkBitableNotifier
from alibabacloud_dingtalk.notable_1_0.models import ListRecordsRequestFilter, ListRecordsRequestFilterConditions

class MyCustomNotifier(BaseDingTalkBitableNotifier):
    def get_operator_id(self) -> str:
        return "YOUR_OPERATOR_ID"
    
    def build_query_filter(self, status=None, **kwargs):
        conditions = []
        
        if status:
            status_condition = ListRecordsRequestFilterConditions()
            status_condition.field = "状态"
            status_condition.operator = 'equal'
            status_condition.value = [status]
            conditions.append(status_condition)
        
        if not conditions:
            return None
            
        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj
    
    def parse_record_to_model(self, record):
        return {
            'id': record.id,
            'title': record.fields.get("标题"),
            'status': record.fields.get("状态")
        }
    
    def should_process_record(self, record, **kwargs):
        return record.fields.get("状态") != "已完成"
    
    def build_update_data(self, record_id: str, **kwargs):
        return {
            "record_id": record_id,
            "fields": kwargs.get("fields", {})
        }
```

### 2. 使用自定义实现

```python
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 创建实例
notifier = MyCustomNotifier(
    logger_=logger,
    app_key="your_app_key",
    app_secret="your_app_secret", 
    agent_id="your_agent_id"
)

# 查询数据
records = notifier.list_bitable_data_generic(
    dentry_uuid="your_base_id",
    id_or_name="your_sheet_name",
    status="进行中"  # 传递给 build_query_filter 的参数
)

# 更新数据
update_data = [
    notifier.build_update_data(
        record_id="record_123",
        fields={"状态": "已完成", "完成时间": "2024-01-01"}
    )
]

success = notifier.update_bitable_data_generic(
    dentry_uuid="your_base_id",
    id_or_name="your_sheet_name", 
    record_updates=update_data
)
```

## 内置实现示例

### 1. DefaultDingTalkBitableNotifier
- 保持原有的默认逻辑
- 支持按更新次数过滤
- 使用7天规则进行记录处理判断

### 2. DMDingTalkBitableNotifier  
- 大毛客户专用实现
- 支持按天数回溯查询
- 针对大毛餐饮表格的数据结构

### 3. TaoTianDingTalkBitableNotifier
- 淘天客户专用实现  
- 使用3天规则而不是7天规则
- 适用于淘天的业务场景

### 4. CustomFieldDingTalkBitableNotifier
- 展示如何处理自定义字段名称
- 支持灵活的字段映射配置
- 可配置操作员ID和字段名称

## 主要方法说明

### 查询方法
```python
records = notifier.list_bitable_data_generic(
    dentry_uuid="base_id",      # 多维表ID
    id_or_name="sheet_name",    # 工作表名称
    **kwargs                    # 传递给子类方法的参数
)
```

### 更新方法
```python
success = notifier.update_bitable_data_generic(
    dentry_uuid="base_id",
    id_or_name="sheet_name", 
    record_updates=[            # 更新数据列表
        {"record_id": "xxx", "fields": {...}},
        {"record_id": "yyy", "fields": {...}}
    ]
)
```

### 插入方法
```python
# 同步版本
success = notifier.batch_insert_bitable_data_generic(
    dentry_uuid="base_id",
    id_or_name="sheet_name",
    records_data=[             # 插入数据列表
        {"field1": "value1", "field2": "value2"},
        {"field1": "value3", "field2": "value4"}
    ]
)

# 异步版本
success = await notifier.batch_insert_bitable_data_generic_async(
    dentry_uuid="base_id",
    id_or_name="sheet_name", 
    records_data=records_data
)
```

### 消息发送
```python
notifier.send_message(
    union_id="user_union_id",
    message="您的任务已处理完成"
)
```

## 辅助工具方法

基类提供了一些有用的辅助方法：

```python
# 计算时间戳距离今天的天数
days = notifier._days_since_timestamp(timestamp_millis)

# 判断是否符合7天规则 (7, 14, 21, 28, 35, 42, 49, 56天)
is_match = notifier._is_match_7_days_rule(created_time, last_modified_time)

# 判断是否符合3天规则 (3天和7天)
is_match = notifier._is_match_3_days_rule(created_time, last_modified_time)
```

## 最佳实践

1. **参数传递**: 使用 `**kwargs` 在方法间传递参数，保持接口的灵活性
2. **错误处理**: 在子类实现中添加适当的异常处理
3. **日志记录**: 充分利用传入的 logger 记录操作日志
4. **配置管理**: 将操作员ID、字段名称等配置项作为构造函数参数
5. **数据验证**: 在 `should_process_record` 中添加数据有效性检查

## 文件结构

```
src/jss_api_extend/bitable/
├── base_dingtalk_bitable_notifier.py    # 基础抽象类
├── client_implementations.py            # 具体客户实现示例  
├── usage_examples.py                    # 使用示例
├── README_base_notifier.md              # 本文档
└── dingtalk_bitable_notifier.py         # 原有实现（保持不变）
```

## 注意事项

1. 确保钉钉应用配置正确（app_key, app_secret, agent_id）
2. 操作员ID必须有相应的多维表操作权限
3. 字段名称必须与多维表中的实际字段名称匹配
4. 批量操作时注意数据量限制，建议单次不超过100条记录
5. 异步方法需要在异步环境中调用

通过这种设计，您可以轻松为不同客户创建定制化的多维表操作实现，同时复用所有通用功能。
