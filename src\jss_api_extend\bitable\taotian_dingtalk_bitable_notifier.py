# -*- coding: utf-8 -*-

import json
from typing import Optional

from alibabacloud_dingtalk.notable_1_0 import models as dingtalknotable__1__0_models
from alibabacloud_dingtalk.notable_1_0.client import Client as dingtalknotable_1_0_Client
from alibabacloud_dingtalk.notable_1_0.models import *
from alibabacloud_tea_util import models as util_models

from bitable.dingtalk_bitable_notifier import DingTalkBitableNotifier
from client.model.dingtalk_bitable_record_info import DingtalkBitableRecordInfo
from dingtalk_alerter import DingTalkAlerter
from utils.http_utils import HttpUtils


class TaoTianDingTalkBitableNotifier(DingTalkBitableNotifier):
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

        self.ding_talk_alert = DingTalkAlerter()
        self.operator_map = None
        self.headers = {
            "Content-Type": "application/json",
        }

    def send_message(self, union_id, message):
        if self.operator_map is None:
            self.operator_map = self.query_operator_info("X6GRezwJlA5GvjnQIn4aeQR68dqbropQ", "运营员工列表")

        phone = None
        try:
            phone = self.operator_map.get(union_id)
        except Exception as e:
            self.logger_.error(e)

        isAtAll = False
        if phone is None:
            isAtAll = True

        url = "https://oapi.dingtalk.com/robot/send?access_token=0f5f45136cdf97b2c9128b95821d73d6f668dc03549a686e3d1bfcba085679fc"
        body = {
            "at": {
                "atMobiles": [
                    phone
                ],
                "isAtAll": isAtAll
            },
            "text": {
                "content": "淘天小助手-作品阈值提醒：" + message
            },
            "msgtype": "text"
        }

        try:
            response = HttpUtils.post(url, headers=self.headers, data=json.dumps(body))
            self.logger_.info(f"send_message={response.text}")
        except Exception as e:
            self.logger_.error(f"{e}")

    def list_bitable_data_by_api_filter(self, dentry_uuid, id_or_name, max_update_times) \
            -> Optional[List[DingtalkBitableRecordInfo]]:
        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()
        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"
        request.max_results = 100
        request.filter = None

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records

            for record in records:
                flag = False
                update_times = int(record.fields.get("更新次数"))
                if update_times > 0:
                    flag = self._is_match_3_days_rule(record.created_time, record.last_modified_time)
                else:
                    flag = True

                if flag:
                    bitable_record_info = DingtalkBitableRecordInfo(
                        work_url=record.fields.get("作品链接"),
                        update_count=record.fields.get("更新次数"),
                        user_union_id=[record.fields.get("运营人员")],
                        row_id=record.id
                    )

                    bitable_record_list.append(bitable_record_info)

        return bitable_record_list

    def query_operator_info(self, dentry_uuid, id_or_name):
        operator_map = {}

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"
        request.max_results = 100
        request.filter = None

        request.next_token = next_token
        response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
            base_id=dentry_uuid,
            sheet_id_or_name=id_or_name,
            request=request,
            headers=headers,
            runtime=util_models.RuntimeOptions())

        if response.status_code != 200:
            return

        records = response.body.records
        for record in records:
            union_id = record.fields.get("人员").get("unionId")
            operator_map[union_id] = record.fields.get("运营人员手机号")

        return operator_map

    def _days_since_timestamp(self, millis_timestamp):
        import datetime

        # 将毫秒时间戳转换为秒
        timestamp_seconds = millis_timestamp / 1000
        # 转换为datetime对象
        timestamp_date = datetime.datetime.fromtimestamp(timestamp_seconds)
        # 获取当前日期（去掉时间部分）
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        # 计算日期差
        delta = today - timestamp_date.replace(hour=0, minute=0, second=0, microsecond=0)

        return delta.days

    def _is_match_3_days_rule(self, created_time, last_modified_time):
        diff_update_days = self._days_since_timestamp(last_modified_time)
        if diff_update_days == 0:
            return False

        diff_create_days = self._days_since_timestamp(created_time)
        return diff_create_days in {3, 7}

    def update_bitable_data_by_api(self,
                                   dentry_uuid,
                                   id_or_name,
                                   d_list: List[UpdateRecordsRequestRecords]) -> bool:
        """
        批量更新钉钉多维表数据 (异步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            d_list (list): 要更新的记录数据列表
        Returns:
            bool: 更新是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.UpdateRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            records = []
            for d in d_list:
                records.append(dingtalknotable__1__0_models.UpdateRecordsRequestRecords.from_map(d))

            request = dingtalknotable__1__0_models.UpdateRecordsRequest()
            request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"
            request.records = records

            response: dingtalknotable__1__0_models.UpdateRecordsResponse = dingtalk_notable.update_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                self.logger_.error(
                    f"批量更新多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"同步多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            updated_count = len(d_list)
            self.logger_.info(f"成功批量更新 {updated_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量更新多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量更新多维表={dentry_uuid} 异常: {str(e)}")
            return False
