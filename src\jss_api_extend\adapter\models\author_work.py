# -*- coding: utf-8 -*-

import datetime


class AuthorWork(object):

    def __init__(self,
                 _id,
                 platform,
                 author_id,
                 author_identity,
                 author_avatar,
                 author_name,
                 author_url,
                 work_id,
                 work_uuid,
                 url,
                 download_url,
                 long_url,
                 digest,
                 title,
                 thumbnail_link,
                 content,
                 img_urls,
                 video_urls,
                 music_url,
                 music_author_name,
                 music_id,
                 music_name,
                 publish_time,
                 publish_day,
                 location_ip,
                 read_count,
                 like_count,
                 comment_count,
                 share_count,
                 collect_count,
                 text_polarity,
                 is_detail,
                 record_time):
        self.id = _id
        self.platform = platform
        self.author_id = author_id
        self.author_identity = author_identity
        self.author_avatar = author_avatar
        self.author_name = author_name
        self.author_url = author_url
        self.work_id = work_id
        self.work_uuid = work_uuid
        self.url = url
        self.download_url = download_url
        self.long_url = long_url
        self.digest = digest
        self.title = title
        self.thumbnail_link = thumbnail_link
        self.content = content
        self.img_urls = img_urls
        self.video_urls = video_urls
        self.music_url = music_url
        self.music_author_name = music_author_name
        self.music_id = music_id
        self.music_name = music_name
        self.publish_time = publish_time
        self.publish_day = publish_day
        self.location_ip = location_ip
        self.read_count = read_count
        self.like_count = like_count
        self.comment_count = comment_count
        self.share_count = share_count
        self.collect_count = collect_count
        self.text_polarity = text_polarity
        self.record_time = record_time
        self.is_detail = is_detail
