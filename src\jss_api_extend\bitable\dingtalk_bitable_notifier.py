# -*- coding: utf-8 -*-

import json
import time
from typing import Optional

from alibabacloud_dingtalk.notable_1_0 import models as dingtalknotable__1__0_models
from alibabacloud_dingtalk.notable_1_0.client import Client as dingtalknotable_1_0_Client
from alibabacloud_dingtalk.notable_1_0.models import *
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

from dingtalk_alerter import DingTalkAlerter
from ..client.model.dingtalk_bitable_record_info import DingtalkBitableRecordInfo, DingTalkBitableDMRecord
from ..utils.http_utils import JSSHttpUtils
from ..utils.time_utils import TimeUtils


class DingTalkBitableNotifier:
    def __init__(self, logger_, app_key, app_secret, agent_id):
        self.logger_ = logger_
        self.client = self._create_client()
        self.access_token = None
        self.expire_time = int(time.time())
        self.headers = {
            "accept": "application/json"
        }

        self.bitable_url = "https://api.dingtalk.com/v1.0/notable/bases/"

        self.app_key = app_key
        self.app_secret = app_secret
        self.agent_id = agent_id

        self.ding_talk_alert = DingTalkAlerter()

    def _create_client(self) -> dingtalkoauth2_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkoauth2_1_0Client(config)

    def get_access_token(self):
        if self.access_token is None or int(time.time()) >= self.expire_time:
            self.__refresh_access_token()

        return self.access_token

    def __refresh_access_token(self):
        flag, access_token, expire_in = self._query_new_access_token()
        if flag:
            self.access_token = access_token
            self.expire_time = int(time.time()) + expire_in - 60 * 5

    def _query_new_access_token(self):
        get_access_token_request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
            app_key=self.app_key,
            app_secret=self.app_secret
        )

        try:
            response: dingtalkoauth_2__1__0_models.GetAccessTokenResponse = self.client.get_access_token(
                get_access_token_request)
            if response.status_code != 200:
                return False, None, None

            body: dingtalkoauth_2__1__0_models.GetAccessTokenResponseBody = response.body
            return True, body.access_token, body.expire_in
        except Exception as err:
            self.logger_.error(f"dingtalk get_access_token error={err}")

    def get_user_id_by_union_id(self, union_id):
        access_token = self.get_access_token()
        url = 'https://oapi.dingtalk.com/topapi/user/getbyunionid?access_token=' + access_token
        body = {
            "unionid": union_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=body)
            self.logger_.info(f'get_user_id_by_union_id={response.text}')
            json_str = response.content.decode('utf-8')
            data = json.loads(json_str)
            return data.get('result').get('userid')
        except Exception as e:
            self.logger_.error(e)

    def send_message(self, union_id, message):
        user_id = self.get_user_id_by_union_id(union_id)

        url = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=" + self.get_access_token()
        body = {
            "agent_id": self.agent_id,
            "msg": {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            },
            "userid_list": user_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=json.dumps(body))
            self.logger_.info(f"send_message={response.text}")
        except Exception as e:
            self.logger_.error(f"{e}")

    def _create_bitable_client(self) -> dingtalkoauth2_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalknotable_1_0_Client(config)

    async def query_bitable_data_by_api(self):
        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.GetRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()

        request = dingtalknotable__1__0_models.GetRecordsRequest()
        request.operator_id = "JQm5uuCxkjI6pjP08ZJ7aQiEiE"
        response: dingtalknotable__1__0_models.GetRecordsResponse = await dingtalk_notable.get_records_with_options_async(
            base_id="G1DKw2zgV2OQYqGMHLO0dxOXJB5r9YAn",
            sheet_id_or_name="基础数据表",
            request=request,
            headers=headers, runtime=util_models.RuntimeOptions())

        print(response)

    def list_bitable_data_by_api(self, dentry_uuid, id_or_name, max_update_times) \
            -> Optional[List[DingtalkBitableRecordInfo]]:
        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()

        update_times_condition = ListRecordsRequestFilterConditions()
        update_times_condition.field = "更新次数"
        update_times_condition.operator = 'lessEqual'
        update_times_condition.value = [max_update_times]

        name_condition = ListRecordsRequestFilterConditions()
        name_condition.field = "作品链接"
        name_condition.operator = 'notEmpty'
        name_condition.value = ['']

        filter = ListRecordsRequestFilter()
        filter.combination = "and"
        filter.conditions = [update_times_condition, name_condition]

        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "JQm5uuCxkjI6pjP08ZJ7aQiEiE"
        request.max_results = 100
        request.filter = filter

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records

            for record in records:
                bitable_record_info = DingtalkBitableRecordInfo(
                    work_url=record.fields.get("作品链接"),
                    update_count=record.fields.get("更新次数"),
                    user_union_id=record.fields.get("运营人员"),
                    row_id=record.id
                )
                bitable_record_list.append(bitable_record_info)

        return bitable_record_list

    def list_bitable_data_by_api_filter(self, dentry_uuid, id_or_name, max_update_times) \
            -> Optional[List[DingtalkBitableRecordInfo]]:
        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()
        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "JQm5uuCxkjI6pjP08ZJ7aQiEiE"
        request.max_results = 100
        request.filter = None

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records

            for record in records:
                flag = False
                update_times = int(record.fields.get("更新次数"))
                if update_times >= max_update_times:
                    flag = self._is_match_7_days_rule(record.created_time, record.last_modified_time)
                else:
                    flag = True

                if flag:
                    bitable_record_info = DingtalkBitableRecordInfo(
                        work_url=record.fields.get("作品链接"),
                        update_count=record.fields.get("更新次数"),
                        user_union_id=record.fields.get("运营人员"),
                        row_id=record.id
                    )

                    bitable_record_list.append(bitable_record_info)

        return bitable_record_list

    def _days_since_timestamp(self, millis_timestamp):
        import datetime

        # 将毫秒时间戳转换为秒
        timestamp_seconds = millis_timestamp / 1000
        # 转换为datetime对象
        timestamp_date = datetime.datetime.fromtimestamp(timestamp_seconds)
        # 获取当前日期（去掉时间部分）
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        # 计算日期差
        delta = today - timestamp_date.replace(hour=0, minute=0, second=0, microsecond=0)

        return delta.days

    def _is_match_7_days_rule(self, created_time, last_modified_time):
        diff_update_days = self._days_since_timestamp(last_modified_time)
        if diff_update_days == 0:
            return False

        diff_create_days = self._days_since_timestamp(created_time)
        return diff_create_days in {7 * n for n in range(1, 9)}

    def list_bitable_data_by_api_dm(self, dentry_uuid: str, id_or_name: str) \
            -> Optional[List[DingTalkBitableDMRecord]]:
        """
        大毛餐饮表格, 仅获取近15天 提交的表格
        :param dentry_uuid:
        :param id_or_name:
        :return:
        """

        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()

        # 小于 当天
        # today = TimeUtils.get_current_ts('%Y-%m-%d')
        # date_filter_le_condition = ListRecordsRequestFilterConditions()
        # date_filter_le_condition.field = "创建时间"
        # date_filter_le_condition.operator = 'less'
        # date_filter_le_condition.value = [today]

        # 大于 15天前
        past_date = TimeUtils.get_past_date(15)
        date_filter_gt_condition = ListRecordsRequestFilterConditions()
        date_filter_gt_condition.field = "创建时间"
        date_filter_gt_condition.operator = 'greater'
        date_filter_gt_condition.value = [past_date]

        # 作品链接不为空
        name_condition = ListRecordsRequestFilterConditions()
        name_condition.field = "链接"
        name_condition.operator = 'notEmpty'
        name_condition.value = ['']

        filter = ListRecordsRequestFilter()
        filter.combination = "and"
        filter.conditions = [date_filter_gt_condition, name_condition]

        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"
        request.max_results = 100
        request.filter = filter

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records

            for record in records:
                bitable_record_info = DingTalkBitableDMRecord(
                    work_url=record.fields.get("链接"),
                    create_time=record.fields.get("创建时间"),
                    row_id=record.id
                )

                bitable_record_list.append(bitable_record_info)

        return bitable_record_list

    def list_bitable_data_by_api_dm_comment(self,
                                            dentry_uuid: str,
                                            id_or_name: str,
                                            work_url: str) \
            -> Optional[List[ListRecordsResponseBodyRecords]]:
        """
        大毛餐饮表格
        :param dentry_uuid:
        :param id_or_name:
        :return:
        """

        bitable_record_list = []

        dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
        headers = dingtalknotable__1__0_models.ListRecordsHeaders()
        headers.x_acs_dingtalk_access_token = self.get_access_token()

        update_times_condition = ListRecordsRequestFilterConditions()
        update_times_condition.field = "作品链接"
        update_times_condition.operator = 'equal'
        update_times_condition.value = [work_url]

        filter = ListRecordsRequestFilter()
        filter.combination = "and"
        filter.conditions = [update_times_condition]

        has_more = True
        next_token = None

        request = dingtalknotable__1__0_models.ListRecordsRequest()
        request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"
        request.max_results = 100
        request.filter = filter

        while has_more:
            request.next_token = next_token
            response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                break

            has_more = response.body.has_more
            next_token = response.body.next_token
            records = response.body.records
            bitable_record_list.extend(records)

        return bitable_record_list

    def update_bitable_data_by_api(self,
                                   dentry_uuid,
                                   id_or_name,
                                   operator_id,
                                   d_list: List[UpdateRecordsRequestRecords]) -> bool:
        """
        批量更新钉钉多维表数据 (异步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            d_list (list): 要更新的记录数据列表
            operator_id: 操作人员

        Returns:
            bool: 更新是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.UpdateRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            records = []
            for d in d_list:
                records.append(dingtalknotable__1__0_models.UpdateRecordsRequestRecords.from_map(d))

            request = dingtalknotable__1__0_models.UpdateRecordsRequest()
            request.operator_id = operator_id
            request.records = records

            response: dingtalknotable__1__0_models.UpdateRecordsResponse = dingtalk_notable.update_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                self.logger_.error(
                    f"批量更新多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"同步多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            updated_count = len(d_list)
            self.logger_.info(f"成功批量更新 {updated_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量更新多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量更新多维表={dentry_uuid} 异常: {str(e)}")
            return False

    async def batch_insert_bitable_data_by_api_sync(self, dentry_uuid: str, id_or_name: str, records_data: list):
        """
        批量插入数据到钉钉多维表 (异步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            records_data (list): 要插入的记录数据列表，每个元素是包含字段数据的字典
                                格式: [{"field1": "value1", "field2": "value2"}, ...]

        Returns:
            bool: 插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                # 创建记录对象
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = "C9KfMJuvGAysoLhyLXfGuwiEiE"  # 操作者ID，可以考虑作为参数传入
            request.records = records

            # 发送批量插入请求 (异步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = await dingtalk_notable.insert_records_with_options_async(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(
                f"成功批量插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 异常: {str(e)}")
            return False

    def batch_insert_bitable_data_by_api_(self,
                                          dentry_uuid: str,
                                          id_or_name: str,
                                          operator_id: str,
                                          records_data: list,
                                          ):
        """
        批量插入数据到钉钉多维表 (同步版本)

        Args:
            dentry_uuid (str): 多维表的base_id
            id_or_name (str): 工作表的ID或名称
            records_data (list): 要插入的记录数据列表，每个元素是包含字段数据的字典
                                格式: [{"field1": "value1", "field2": "value2"}, ...]

        Returns:
            bool: 插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = operator_id
            request.records = records

            # 发送批量插入请求 (同步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = dingtalk_notable.insert_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(
                f"成功批量插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"批量插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 异常: {str(e)}")
            return False
