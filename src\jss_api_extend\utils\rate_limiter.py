# -*- coding: utf-8 -*-
"""
API速率限制器
用于控制API调用频率，避免超过QPS限制
"""

import time
import threading
from typing import Dict, Optional
from collections import deque


class RateLimiter:
    """
    速率限制器，支持令牌桶算法
    """
    
    def __init__(self, max_calls: int, time_window: float = 1.0):
        """
        初始化速率限制器
        
        Args:
            max_calls: 时间窗口内最大调用次数
            time_window: 时间窗口大小（秒）
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = deque()
        self.lock = threading.Lock()
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """
        获取调用许可
        
        Args:
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            bool: 是否成功获取许可
        """
        start_time = time.time()
        
        while True:
            with self.lock:
                current_time = time.time()
                
                # 清理过期的调用记录
                while self.calls and current_time - self.calls[0] >= self.time_window:
                    self.calls.popleft()
                
                # 检查是否可以进行调用
                if len(self.calls) < self.max_calls:
                    self.calls.append(current_time)
                    return True
            
            # 检查超时
            if timeout is not None and time.time() - start_time >= timeout:
                return False
            
            # 等待一小段时间后重试
            time.sleep(0.1)
    
    def get_wait_time(self) -> float:
        """
        获取需要等待的时间
        
        Returns:
            float: 等待时间（秒）
        """
        with self.lock:
            if len(self.calls) < self.max_calls:
                return 0.0
            
            # 计算最早的调用何时过期
            oldest_call = self.calls[0]
            wait_time = self.time_window - (time.time() - oldest_call)
            return max(0.0, wait_time)


class PlatformRateLimiter:
    """
    平台特定的速率限制器管理器
    """
    
    def __init__(self, logger=None):
        self.logger = logger
        self.limiters: Dict[str, RateLimiter] = {}
        self.lock = threading.Lock()
        
        # 默认平台限制配置
        self.platform_limits = {
            "dy": {"max_calls": 2, "time_window": 1.0},      # 抖音: 2 QPS
            "xhs": {"max_calls": 2, "time_window": 1.0},     # 小红书: 2 QPS
            "default": {"max_calls": 5, "time_window": 1.0}  # 默认: 5 QPS
        }
    
    def get_limiter(self, platform: str) -> RateLimiter:
        """
        获取指定平台的速率限制器
        
        Args:
            platform: 平台名称
            
        Returns:
            RateLimiter: 速率限制器实例
        """
        with self.lock:
            if platform not in self.limiters:
                config = self.platform_limits.get(platform, self.platform_limits["default"])
                self.limiters[platform] = RateLimiter(
                    max_calls=config["max_calls"],
                    time_window=config["time_window"]
                )
                
                if self.logger:
                    self.logger.info(f"创建平台 {platform} 的速率限制器: {config['max_calls']} calls/{config['time_window']}s")
            
            return self.limiters[platform]
    
    def acquire_for_platform(self, platform: str, timeout: Optional[float] = 10.0) -> bool:
        """
        为指定平台获取调用许可
        
        Args:
            platform: 平台名称
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取许可
        """
        limiter = self.get_limiter(platform)
        
        if self.logger:
            wait_time = limiter.get_wait_time()
            if wait_time > 0:
                self.logger.info(f"平台 {platform} 需要等待 {wait_time:.2f}秒 (QPS限制)")
        
        success = limiter.acquire(timeout)
        
        if not success and self.logger:
            self.logger.warning(f"平台 {platform} 获取调用许可超时")
        
        return success
    
    def update_platform_limit(self, platform: str, max_calls: int, time_window: float = 1.0):
        """
        更新平台的速率限制
        
        Args:
            platform: 平台名称
            max_calls: 最大调用次数
            time_window: 时间窗口（秒）
        """
        with self.lock:
            self.platform_limits[platform] = {
                "max_calls": max_calls,
                "time_window": time_window
            }
            
            # 如果已存在限制器，需要重新创建
            if platform in self.limiters:
                del self.limiters[platform]
            
            if self.logger:
                self.logger.info(f"更新平台 {platform} 的速率限制: {max_calls} calls/{time_window}s")
    
    def get_status(self) -> Dict[str, Dict]:
        """
        获取所有平台的状态信息
        
        Returns:
            Dict: 状态信息
        """
        status = {}
        
        with self.lock:
            for platform, limiter in self.limiters.items():
                with limiter.lock:
                    status[platform] = {
                        "max_calls": limiter.max_calls,
                        "time_window": limiter.time_window,
                        "current_calls": len(limiter.calls),
                        "wait_time": limiter.get_wait_time()
                    }
        
        return status


# 全局速率限制器实例
_global_platform_limiter = None


def get_global_platform_limiter(logger=None) -> PlatformRateLimiter:
    """
    获取全局平台速率限制器实例
    
    Args:
        logger: 日志记录器
        
    Returns:
        PlatformRateLimiter: 全局速率限制器实例
    """
    global _global_platform_limiter
    
    if _global_platform_limiter is None:
        _global_platform_limiter = PlatformRateLimiter(logger)
    
    return _global_platform_limiter
