# -*- coding: utf-8 -*-

import json

import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

from adapter.models.author_work import AuthorWork
from utils.time_utils import TimeUtils


class LarkBitable:
    def __init__(self, logger_, app_id, app_secret, app_token, article_table_id):
        self.client = lark.Client.builder() \
            .app_id(app_id) \
            .app_secret(app_secret) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        self.app_token = app_token
        self.article_table_id = article_table_id
        self.logger_ = logger_

    def insert_article_record(self, keyword, author_work: AuthorWork):
        # 构造请求对象
        request: CreateAppTableRecordRequest = CreateAppTableRecordRequest.builder() \
            .app_token(self.app_token).table_id(self.article_table_id) \
            .request_body(self._build_article_app_table_record(keyword, author_work)).build()

        # 发起请求
        response: CreateAppTableRecordResponse = self.client.bitable.v1.app_table_record.create(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.bitable.v1.app_table_record.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    def batch_insert_article_records(self, keyword, author_work_list: list):
        try:
            record_list = []
            for author_work in author_work_list:
                record_list.append(self._build_article_app_table_record(keyword, author_work))

            # 构造请求对象
            request: BatchCreateAppTableRecordRequest = BatchCreateAppTableRecordRequest.builder() \
                .app_token(self.app_token).table_id(self.article_table_id) \
                .request_body(BatchCreateAppTableRecordRequestBody.builder()
                              .records(record_list).build()).build()

            # 发起请求
            response: BatchCreateAppTableRecordResponse = self.client.bitable.v1.app_table_record.batch_create(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.bitable.v1.app_table_record.batch_create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return

            # 处理业务结果
            lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        except Exception as e:
            print(e)

    def query_article_record(self, work_id: str):
        try:
            request: SearchAppTableRecordRequest = SearchAppTableRecordRequest.builder() \
                .app_token(self.app_token).table_id(self.article_table_id) \
                .page_size(20) \
                .request_body(SearchAppTableRecordRequestBody.builder()
                              .filter(FilterInfo.builder()
                                      .conjunction("and")
                                      .conditions([Condition.builder()
                                                  .field_name("UID")
                                                  .operator("is")
                                                  .value([work_id])
                                                  .build()])
                                      .build())
                              .automatic_fields(False)
                              .build()) \
                .build()

            # 发起请求
            response: SearchAppTableRecordResponse = self.client.bitable.v1.app_table_record.search(request)
            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.bitable.v1.app_table_record.search failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return None

            d: SearchAppTableRecordResponseBody = response.data
            if d is None or d.total == 0:
                return None

            items = d.items
            if items is None or len(items) == 0:
                return None

            record: AppTableRecord = items[0]
            return record.record_id
        except Exception as e:
            self.logger_.error(e)

    def update_article_record(self, keyword, record_id: str, author_work: AuthorWork):
        # 构造请求对象
        request: UpdateAppTableRecordRequest = UpdateAppTableRecordRequest.builder() \
            .app_token(self.app_token).table_id(self.article_table_id).record_id(record_id) \
            .request_body(self._build_article_app_table_record(keyword, author_work)) \
            .build()

        # 发起请求
        response: UpdateAppTableRecordResponse = self.client.bitable.v1.app_table_record.update(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.bitable.v1.app_table_record.update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    def _build_article_app_table_record(self, keyword, author_work: AuthorWork):
        return AppTableRecord.builder().fields(self._build_article_detail_record(keyword, author_work)).build()

    def _build_article_detail_record(self, keyword, author_work: AuthorWork):
        ts_ms = TimeUtils.dt_str_to_ms(author_work.publish_time)

        return {
            "平台": "小红书",
            "关键词": keyword,
            "标题": author_work.title,
            "作品链接": author_work.url,
            "正文": author_work.content,
            "图文链接": author_work.thumbnail_link,
            "发布时间": ts_ms,
            "作者昵称": author_work.author_name,
            "作者主页": author_work.author_url,
            "点赞数": author_work.like_count,
            "评论数": author_work.comment_count,
            "转发数": author_work.share_count,
            "收藏数": author_work.collect_count,
            "UID": author_work.work_id
        }

