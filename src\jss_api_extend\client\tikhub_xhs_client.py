# -*- coding: utf-8 -*-

import time
from typing import Optional

from ..utils.http_utils import JSSHttpUtils
from ..config.tikhub_config import tikhub_config


class TikhubXhsClient:
    def __init__(self,
                 logger_,
                 max_retries: int = 3,
                 retry_delay: int = 2):

        self.base_url = tikhub_config.get("base_url")
        self.authorization = tikhub_config.get("api_key")
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {self.authorization}"
        }
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self.logger_ = logger_

    def __process_request(self, url: str, headers: dict, method: str, params: dict = None) -> Optional[dict]:

        for attempt in range(self._max_retries):
            try:
                if method == "GET":
                    response = JSSHttpUtils.get(url=url, headers=headers)
                else:
                    response = JSSHttpUtils.post(url, data=params, headers=headers)

                if not response or \
                        not response.text.strip() or \
                        not response.content:
                    error_message = "第 {0} 次响应内容为空, 状态码: {1}, URL:{2}".format(attempt + 1,
                                                                              response.status_code,
                                                                              response.url)
                    if self.logger_:
                        self.logger_.info(error_message)

                    if attempt < self._max_retries - 1:
                        if self.logger_:
                            self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                        time.sleep(self._retry_delay)
                    continue

                response.raise_for_status()
                return response.json()

            except Exception as e:
                if self.logger_:
                    self.logger_.error(f"第 {attempt + 1} 次请求发生异常: {str(e)}, URL: {url}")
                if attempt < self._max_retries - 1:
                    if self.logger_:
                        self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(self._retry_delay)
                else:
                    return None

        if self.logger_:
            self.logger_.error(f"所有 {self._max_retries} 次重试都失败了，URL: {url}")
        return None

    def get_user_notes(self, user_id: str, cursor: str = None) -> dict:
        """
            查询用户笔记 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_home_notes"

        url = self.base_url + f"{endpoint}?user_id={user_id}&cursor"
        user_notes: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return user_notes

    def search_notes_web_v1(self, keyword: str,
                            page: int = 1,
                            sort: str = "general",
                            noteType: str = "_0",
                            noteTime: str = ""):
        """
            获取搜索数据 v1
            keyword: 搜索关键词
            page: 页码，默认为1
            sort: 排序方式
                综合排序（默认参数）: general
                最热排序: popularity_descending
                最新排序: time_descending
                最多评论: comment_descending
                最多收藏: collect_descending
            noteType: 笔记类型
                综合笔记（默认参数）: _0
                视频笔记: _1
                图文笔记: _2
                直播: _3
            noteTime: 发布时间
                不限: ""
                一天内 :一天内
                一周内 :一周内
                半年内 :半年内
        """
        endpoint = "/api/v1/xiaohongshu/web/search_notes"
        url: str = self.base_url + f"{endpoint}?keyword={keyword}&page={page}&sort={sort}&noteType={noteType}&noteTime={noteTime}"

        search_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return search_result

    def search_notes_web_v2(self, keyword: str,
                            page: int = 1,
                            sort: str = "general",
                            noteType: str = "0",
                            noteTime: str = ""):
        """
            获取搜索数据 v2
            keywords：搜索关键词
            sort_type：排序方式
                general：综合
                time_descending：最新
                popularity_descending：最热
            note_type: 笔记类型
                0：全部
                1：视频
                2：图文
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_search_notes"
        url: str = self.base_url + f"{endpoint}?keywords={keyword}&page={page}&sort_type={sort}&note_type={noteType}"

        search_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return search_result

    def fetch_feed_notes(self, note_id: str):
        """
            获取搜索数据 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_feed_notes"
        url: str = self.base_url + f"{endpoint}?note_id={note_id}"

        note_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return note_result

    def fetch_feed_notes_v2_2(self, note_id: str):
        """
            获取搜索数据 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_feed_notes_v2"
        url: str = self.base_url + f"{endpoint}?note_id={note_id}"

        note_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return note_result

    def search_notes_app_v1(self, keyword, search_id, session_id,
                            page: int = 1, sort_type: str = "general",
                            filter_note_type: str = "不限", filter_note_time="不限"):
        """
            获取搜索数据 v2
        """
        endpoint = "/api/v1/xiaohongshu/app/search_notes"
        url: str = self.base_url + f"{endpoint}?keyword={keyword}&" \
                                   f"page={page}&" \
                                   f"search_id={search_id}&" \
                                   f"session_id={session_id}&" \
                                   f"sort_type={sort_type}&" \
                                   f"filter_note_type={filter_note_type}&" \
                                   f"filter_note_time={filter_note_time}"

        note_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return note_result
