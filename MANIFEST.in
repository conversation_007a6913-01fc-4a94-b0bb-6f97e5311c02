# Include the README and license files
include README.md
include LICENSE
include MANIFEST.in
include pyproject.toml
include setup.cfg
include pytest.ini

# Include all Python files in the source directory
recursive-include src *.py

# Include configuration files
recursive-include src *.json
recursive-include src *.yaml
recursive-include src *.yml
recursive-include src *.txt
recursive-include src *.cfg

# Include examples
recursive-include examples *.py
include examples/README.md

# Include tests
recursive-include tests *.py

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude *.so
global-exclude .DS_Store

# Exclude development and build files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude tox.ini
exclude Makefile
prune .git
prune .github
prune .vscode
prune .idea
prune build
prune dist
prune *.egg-info
