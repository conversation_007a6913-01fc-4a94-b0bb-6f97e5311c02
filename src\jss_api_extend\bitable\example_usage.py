# -*- coding: utf-8 -*-

"""
DingTalk Bitable Notifier 使用示例
展示如何为不同客户创建定制化的实现
"""

import logging
from typing import Dict, Any, Optional
from alibabacloud_dingtalk.notable_1_0.models import ListRecordsRequestFilter, ListRecordsRequestFilterConditions

from dingtalk_bitable_notifier import BaseDingTalkBitableNotifier, DefaultDingTalkBitableNotifier, DMDingTalkBitableNotifier


# 示例1: 创建一个新的客户实现
class XiaoHongShuNotifier(BaseDingTalkBitableNotifier):
    """小红书客户的定制化实现"""
    
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

    def get_operator_id(self) -> str:
        return "XHS_OPERATOR_ID_123"

    def build_query_filter(self, platform="xiaohongshu", min_likes=100, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建小红书客户的查询过滤条件"""
        conditions = []
        
        # 平台过滤
        if platform:
            platform_condition = ListRecordsRequestFilterConditions()
            platform_condition.field = "平台"
            platform_condition.operator = 'equal'
            platform_condition.value = [platform]
            conditions.append(platform_condition)
            
        # 点赞数过滤
        if min_likes:
            likes_condition = ListRecordsRequestFilterConditions()
            likes_condition.field = "点赞数"
            likes_condition.operator = 'greaterEqual'
            likes_condition.value = [min_likes]
            conditions.append(likes_condition)

        # 内容不为空
        content_condition = ListRecordsRequestFilterConditions()
        content_condition.field = "内容链接"
        content_condition.operator = 'notEmpty'
        content_condition.value = ['']
        conditions.append(content_condition)

        if not conditions:
            return None

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> Dict[str, Any]:
        """解析为小红书客户的记录模型"""
        return {
            'id': record.id,
            'content_url': record.fields.get("内容链接"),
            'platform': record.fields.get("平台"),
            'likes_count': record.fields.get("点赞数", 0),
            'comments_count': record.fields.get("评论数", 0),
            'author': record.fields.get("作者"),
            'created_time': record.created_time,
            'last_modified_time': record.last_modified_time
        }

    def should_process_record(self, record, min_engagement_rate=0.05, **kwargs) -> bool:
        """小红书客户的记录处理判断逻辑"""
        likes = record.fields.get("点赞数", 0)
        comments = record.fields.get("评论数", 0)
        views = record.fields.get("浏览数", 1)  # 避免除零
        
        # 计算互动率
        engagement_rate = (likes + comments) / views if views > 0 else 0
        
        return engagement_rate >= min_engagement_rate

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建小红书客户的更新数据"""
        fields = kwargs.get("fields", {})
        
        # 添加更新时间戳
        import time
        fields["最后处理时间"] = int(time.time() * 1000)
        
        return {
            "record_id": record_id,
            "fields": fields
        }


# 示例2: 电商客户实现
class ECommerceNotifier(BaseDingTalkBitableNotifier):
    """电商客户的定制化实现"""
    
    def __init__(self, logger_, app_key, app_secret, agent_id):
        super().__init__(logger_, app_key, app_secret, agent_id)

    def get_operator_id(self) -> str:
        return "ECOMMERCE_OPERATOR_ID_456"

    def build_query_filter(self, order_status="pending", min_amount=0, **kwargs) -> Optional[ListRecordsRequestFilter]:
        """构建电商客户的查询过滤条件"""
        conditions = []
        
        # 订单状态过滤
        if order_status:
            status_condition = ListRecordsRequestFilterConditions()
            status_condition.field = "订单状态"
            status_condition.operator = 'equal'
            status_condition.value = [order_status]
            conditions.append(status_condition)
            
        # 订单金额过滤
        if min_amount > 0:
            amount_condition = ListRecordsRequestFilterConditions()
            amount_condition.field = "订单金额"
            amount_condition.operator = 'greaterEqual'
            amount_condition.value = [min_amount]
            conditions.append(amount_condition)

        if not conditions:
            return None

        filter_obj = ListRecordsRequestFilter()
        filter_obj.combination = "and"
        filter_obj.conditions = conditions
        return filter_obj

    def parse_record_to_model(self, record) -> Dict[str, Any]:
        """解析为电商客户的记录模型"""
        return {
            'id': record.id,
            'order_id': record.fields.get("订单号"),
            'customer_name': record.fields.get("客户姓名"),
            'order_amount': record.fields.get("订单金额", 0),
            'order_status': record.fields.get("订单状态"),
            'product_name': record.fields.get("商品名称"),
            'created_time': record.created_time
        }

    def should_process_record(self, record, priority_customers=None, **kwargs) -> bool:
        """电商客户的记录处理判断逻辑"""
        customer_name = record.fields.get("客户姓名", "")
        
        # 如果指定了优先客户列表，只处理优先客户的订单
        if priority_customers:
            return customer_name in priority_customers
            
        return True

    def build_update_data(self, record_id: str, **kwargs) -> Dict[str, Any]:
        """构建电商客户的更新数据"""
        fields = kwargs.get("fields", {})
        
        # 添加处理标记
        fields["已处理"] = True
        fields["处理人"] = kwargs.get("processor", "系统")
        
        return {
            "record_id": record_id,
            "fields": fields
        }


def main():
    """使用示例"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 配置信息（实际使用时请替换为真实的配置）
    app_key = "your_app_key"
    app_secret = "your_app_secret"
    agent_id = "your_agent_id"
    
    # 示例1: 使用默认实现
    print("=== 使用默认实现 ===")
    default_notifier = DefaultDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    # 查询数据
    try:
        records = default_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            max_update_times=5
        )
        print(f"默认实现查询到 {len(records) if records else 0} 条记录")
    except Exception as e:
        print(f"默认实现查询失败: {e}")
    
    # 示例2: 使用大毛客户实现
    print("\n=== 使用大毛客户实现 ===")
    dm_notifier = DMDingTalkBitableNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        dm_records = dm_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            days_back=15
        )
        print(f"大毛客户实现查询到 {len(dm_records) if dm_records else 0} 条记录")
    except Exception as e:
        print(f"大毛客户实现查询失败: {e}")
    
    # 示例3: 使用小红书客户实现
    print("\n=== 使用小红书客户实现 ===")
    xhs_notifier = XiaoHongShuNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        xhs_records = xhs_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            platform="xiaohongshu",
            min_likes=100,
            min_engagement_rate=0.05
        )
        print(f"小红书客户实现查询到 {len(xhs_records) if xhs_records else 0} 条记录")
    except Exception as e:
        print(f"小红书客户实现查询失败: {e}")
    
    # 示例4: 使用电商客户实现
    print("\n=== 使用电商客户实现 ===")
    ecommerce_notifier = ECommerceNotifier(
        logger_=logger,
        app_key=app_key,
        app_secret=app_secret,
        agent_id=agent_id
    )
    
    try:
        ecommerce_records = ecommerce_notifier.list_bitable_data_generic(
            dentry_uuid="your_base_id",
            id_or_name="your_sheet_name",
            order_status="pending",
            min_amount=100,
            priority_customers=["VIP客户1", "VIP客户2"]
        )
        print(f"电商客户实现查询到 {len(ecommerce_records) if ecommerce_records else 0} 条记录")
    except Exception as e:
        print(f"电商客户实现查询失败: {e}")
    
    # 示例5: 更新数据
    print("\n=== 更新数据示例 ===")
    if xhs_records and len(xhs_records) > 0:
        # 使用小红书客户实现更新数据
        update_data = [
            xhs_notifier.build_update_data(
                record_id=xhs_records[0]['id'],
                fields={"处理状态": "已分析", "分析结果": "高质量内容"}
            )
        ]
        
        try:
            success = xhs_notifier.update_bitable_data_generic(
                dentry_uuid="your_base_id",
                id_or_name="your_sheet_name",
                record_updates=update_data
            )
            print(f"更新结果: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"更新失败: {e}")


if __name__ == "__main__":
    main()
